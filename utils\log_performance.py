#!/usr/bin/env python3
"""
Log Performance and Monitoring Enhancements for TNGD Backup System

This module provides performance monitoring and optimization features for logging:
- Adaptive logging based on system load
- Performance metrics collection and analysis
- Log aggregation and summarization
- Resource usage monitoring
- Automated performance tuning
- Integration hooks for monitoring systems

Performance Features:
- Adaptive verbosity based on system performance
- Asynchronous logging for high-throughput scenarios
- Log buffering and batching for efficiency
- Performance metrics tracking
- Resource usage monitoring
- Automated log level adjustment
"""

import time
import threading
import datetime
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path

# Try to import psutil, fall back to basic monitoring if not available
try:
    import psutil
    HAS_PSUTIL = True
except ImportError:
    HAS_PSUTIL = False
    print("Warning: psutil not available, using basic performance monitoring")


class PerformanceLevel(Enum):
    """Performance levels for adaptive logging."""
    HIGH_PERFORMANCE = "high_performance"    # Minimal logging, maximum performance
    BALANCED = "balanced"                    # Balanced logging and performance
    DETAILED = "detailed"                    # Detailed logging, some performance impact
    DEBUG = "debug"                         # Full debug logging, performance secondary


class SystemLoad(Enum):
    """System load levels."""
    LOW = "low"           # < 30% CPU, < 50% Memory
    MODERATE = "moderate" # 30-70% CPU, 50-80% Memory
    HIGH = "high"         # 70-90% CPU, 80-95% Memory
    CRITICAL = "critical" # > 90% CPU, > 95% Memory


@dataclass
class PerformanceMetrics:
    """Performance metrics for logging operations."""
    timestamp: datetime.datetime
    operation_type: str
    duration_ms: float
    log_entries_count: int
    memory_usage_mb: float
    cpu_usage_percent: float
    disk_io_bytes: int
    correlation_id: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class SystemMetrics:
    """System-wide performance metrics."""
    timestamp: datetime.datetime
    cpu_percent: float
    memory_percent: float
    disk_usage_percent: float
    network_io_bytes: int
    active_log_handlers: int
    log_queue_size: int
    metadata: Dict[str, Any] = field(default_factory=dict)


class PerformanceMonitor:
    """
    Monitors system performance and adjusts logging behavior accordingly.
    """

    def __init__(self, check_interval: int = 30, history_size: int = 100):
        """
        Initialize the performance monitor.

        Args:
            check_interval: Seconds between performance checks
            history_size: Number of metrics to keep in history
        """
        self.check_interval = check_interval
        self.history_size = history_size
        self.metrics_history: List[SystemMetrics] = []
        self.performance_callbacks: List[Callable[[SystemLoad], None]] = []
        self.current_load = SystemLoad.LOW
        self.monitoring = False
        self.monitor_thread = None
        self._lock = threading.Lock()

    def start_monitoring(self):
        """Start the performance monitoring thread."""
        if not self.monitoring:
            self.monitoring = True
            self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self.monitor_thread.start()

    def stop_monitoring(self):
        """Stop the performance monitoring thread."""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)

    def _monitor_loop(self):
        """Main monitoring loop."""
        while self.monitoring:
            try:
                # Collect system metrics
                metrics = self._collect_system_metrics()

                with self._lock:
                    # Add to history
                    self.metrics_history.append(metrics)

                    # Trim history if needed
                    if len(self.metrics_history) > self.history_size:
                        self.metrics_history.pop(0)

                    # Determine current load level
                    new_load = self._calculate_system_load(metrics)

                    # If load level changed, notify callbacks
                    if new_load != self.current_load:
                        self.current_load = new_load
                        for callback in self.performance_callbacks:
                            try:
                                callback(new_load)
                            except Exception as e:
                                print(f"Error in performance callback: {e}")

                time.sleep(self.check_interval)

            except Exception as e:
                print(f"Error in performance monitoring: {e}")
                time.sleep(self.check_interval)

    def _collect_system_metrics(self) -> SystemMetrics:
        """Collect current system performance metrics."""
        try:
            if HAS_PSUTIL:
                # CPU and memory usage
                cpu_percent = psutil.cpu_percent(interval=1)
                memory = psutil.virtual_memory()

                # Disk usage for logs directory
                disk_usage = psutil.disk_usage('logs' if Path('logs').exists() else '.')

                # Network I/O (simplified)
                network_io = psutil.net_io_counters()
                network_bytes = network_io.bytes_sent + network_io.bytes_recv if network_io else 0
            else:
                # Basic fallback metrics
                cpu_percent = 0.0
                memory_percent = 0.0
                disk_usage_percent = 0.0
                network_bytes = 0

            return SystemMetrics(
                timestamp=datetime.datetime.now(),
                cpu_percent=cpu_percent if HAS_PSUTIL else 0.0,
                memory_percent=memory.percent if HAS_PSUTIL else 0.0,
                disk_usage_percent=(disk_usage.used / disk_usage.total) * 100 if HAS_PSUTIL else 0.0,
                network_io_bytes=network_bytes if HAS_PSUTIL else 0,
                active_log_handlers=0,  # Will be updated by logging system
                log_queue_size=0,       # Will be updated by async logger
                metadata={'psutil_available': HAS_PSUTIL}
            )

        except Exception as e:
            print(f"Error collecting system metrics: {e}")
            # Return default metrics
            return SystemMetrics(
                timestamp=datetime.datetime.now(),
                cpu_percent=0.0,
                memory_percent=0.0,
                disk_usage_percent=0.0,
                network_io_bytes=0,
                active_log_handlers=0,
                log_queue_size=0,
                metadata={'error': str(e), 'psutil_available': HAS_PSUTIL}
            )

    def _calculate_system_load(self, metrics: SystemMetrics) -> SystemLoad:
        """Calculate system load level based on metrics."""
        # Define thresholds
        if metrics.cpu_percent > 90 or metrics.memory_percent > 95:
            return SystemLoad.CRITICAL
        elif metrics.cpu_percent > 70 or metrics.memory_percent > 80:
            return SystemLoad.HIGH
        elif metrics.cpu_percent > 30 or metrics.memory_percent > 50:
            return SystemLoad.MODERATE
        else:
            return SystemLoad.LOW

    def register_callback(self, callback: Callable[[SystemLoad], None]):
        """Register a callback for load level changes."""
        self.performance_callbacks.append(callback)

    def get_current_load(self) -> SystemLoad:
        """Get the current system load level."""
        return self.current_load

    def get_metrics_summary(self) -> Dict[str, Any]:
        """Get a summary of recent performance metrics."""
        with self._lock:
            if not self.metrics_history:
                return {}

            recent_metrics = self.metrics_history[-10:]  # Last 10 measurements

            return {
                'current_load': self.current_load.value,
                'avg_cpu_percent': sum(m.cpu_percent for m in recent_metrics) / len(recent_metrics),
                'avg_memory_percent': sum(m.memory_percent for m in recent_metrics) / len(recent_metrics),
                'avg_disk_usage_percent': sum(m.disk_usage_percent for m in recent_metrics) / len(recent_metrics),
                'measurements_count': len(recent_metrics),
                'last_updated': recent_metrics[-1].timestamp.isoformat()
            }


class AdaptiveLogger:
    """
    Logger that adapts its behavior based on system performance.
    """

    def __init__(self, base_logger, performance_monitor: PerformanceMonitor):
        """
        Initialize the adaptive logger.

        Args:
            base_logger: Base logger to wrap
            performance_monitor: Performance monitor instance
        """
        self.base_logger = base_logger
        self.performance_monitor = performance_monitor
        self.performance_level = PerformanceLevel.BALANCED
        self.metrics_collector = LogMetricsCollector()

        # Register for performance updates
        self.performance_monitor.register_callback(self._on_load_change)

        # Performance level mappings
        self.level_mappings = {
            SystemLoad.CRITICAL: PerformanceLevel.HIGH_PERFORMANCE,
            SystemLoad.HIGH: PerformanceLevel.BALANCED,
            SystemLoad.MODERATE: PerformanceLevel.DETAILED,
            SystemLoad.LOW: PerformanceLevel.DEBUG
        }

    def _on_load_change(self, new_load: SystemLoad):
        """Handle system load changes."""
        old_level = self.performance_level
        self.performance_level = self.level_mappings.get(new_load, PerformanceLevel.BALANCED)

        if old_level != self.performance_level:
            print(f"Adaptive logging: Changed from {old_level.value} to {self.performance_level.value} due to {new_load.value} system load")

    def should_log(self, level: str, operation_type: str = "general") -> bool:
        """
        Determine if a log message should be written based on current performance level.

        Args:
            level: Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
            operation_type: Type of operation being logged

        Returns:
            True if the message should be logged
        """
        # Always log errors and critical messages
        if level in ['ERROR', 'CRITICAL']:
            return True

        # Performance-based filtering
        if self.performance_level == PerformanceLevel.HIGH_PERFORMANCE:
            return level in ['WARNING', 'ERROR', 'CRITICAL']
        elif self.performance_level == PerformanceLevel.BALANCED:
            return level in ['INFO', 'WARNING', 'ERROR', 'CRITICAL']
        elif self.performance_level == PerformanceLevel.DETAILED:
            return True  # Log everything except DEBUG in high-load scenarios
        else:  # DEBUG level
            return True  # Log everything

    def log_with_performance_tracking(self, level: str, message: str, operation_type: str = "general",
                                    correlation_id: Optional[str] = None, **kwargs):
        """
        Log a message with performance tracking.

        Args:
            level: Log level
            message: Log message
            operation_type: Type of operation
            correlation_id: Optional correlation ID
            **kwargs: Additional logging arguments
        """
        start_time = time.time()

        try:
            # Check if we should log this message
            if not self.should_log(level, operation_type):
                return

            # Log the message
            log_method = getattr(self.base_logger, level.lower(), self.base_logger.info)
            log_method(message, **kwargs)

            # Collect performance metrics
            duration_ms = (time.time() - start_time) * 1000
            self.metrics_collector.record_log_operation(
                operation_type, duration_ms, 1, correlation_id
            )

        except Exception as e:
            print(f"Error in adaptive logging: {e}")


class LogMetricsCollector:
    """
    Collects and analyzes logging performance metrics.
    """

    def __init__(self, max_metrics: int = 1000):
        """
        Initialize the metrics collector.

        Args:
            max_metrics: Maximum number of metrics to keep in memory
        """
        self.max_metrics = max_metrics
        self.metrics: List[PerformanceMetrics] = []
        self._lock = threading.Lock()

    def record_log_operation(self, operation_type: str, duration_ms: float,
                           log_count: int, correlation_id: Optional[str] = None):
        """
        Record metrics for a logging operation.

        Args:
            operation_type: Type of operation
            duration_ms: Duration in milliseconds
            log_count: Number of log entries
            correlation_id: Optional correlation ID
        """
        try:
            # Get current system metrics (basic version without psutil)
            memory_mb = 0.0
            cpu_percent = 0.0

            if HAS_PSUTIL:
                process = psutil.Process()
                memory_mb = process.memory_info().rss / 1024 / 1024
                cpu_percent = process.cpu_percent()

            metric = PerformanceMetrics(
                timestamp=datetime.datetime.now(),
                operation_type=operation_type,
                duration_ms=duration_ms,
                log_entries_count=log_count,
                memory_usage_mb=memory_mb,
                cpu_usage_percent=cpu_percent,
                disk_io_bytes=0,  # Could be enhanced to track actual disk I/O
                correlation_id=correlation_id
            )

            with self._lock:
                self.metrics.append(metric)

                # Trim metrics if needed
                if len(self.metrics) > self.max_metrics:
                    self.metrics.pop(0)

        except Exception as e:
            print(f"Error recording log metrics: {e}")

    def get_performance_summary(self, operation_type: Optional[str] = None,
                              last_n_minutes: int = 60) -> Dict[str, Any]:
        """
        Get performance summary for logging operations.

        Args:
            operation_type: Optional filter by operation type
            last_n_minutes: Consider only metrics from last N minutes

        Returns:
            Performance summary dictionary
        """
        cutoff_time = datetime.datetime.now() - datetime.timedelta(minutes=last_n_minutes)

        with self._lock:
            # Filter metrics
            filtered_metrics = [
                m for m in self.metrics
                if m.timestamp >= cutoff_time and
                (operation_type is None or m.operation_type == operation_type)
            ]

            if not filtered_metrics:
                return {'message': 'No metrics available for the specified criteria'}

            # Calculate summary statistics
            total_operations = len(filtered_metrics)
            total_log_entries = sum(m.log_entries_count for m in filtered_metrics)
            avg_duration = sum(m.duration_ms for m in filtered_metrics) / total_operations
            max_duration = max(m.duration_ms for m in filtered_metrics)
            min_duration = min(m.duration_ms for m in filtered_metrics)
            avg_memory = sum(m.memory_usage_mb for m in filtered_metrics) / total_operations

            # Group by operation type
            by_operation = {}
            for metric in filtered_metrics:
                op_type = metric.operation_type
                if op_type not in by_operation:
                    by_operation[op_type] = []
                by_operation[op_type].append(metric)

            operation_summaries = {}
            for op_type, op_metrics in by_operation.items():
                operation_summaries[op_type] = {
                    'count': len(op_metrics),
                    'avg_duration_ms': sum(m.duration_ms for m in op_metrics) / len(op_metrics),
                    'total_log_entries': sum(m.log_entries_count for m in op_metrics)
                }

            return {
                'time_period_minutes': last_n_minutes,
                'total_operations': total_operations,
                'total_log_entries': total_log_entries,
                'avg_duration_ms': avg_duration,
                'max_duration_ms': max_duration,
                'min_duration_ms': min_duration,
                'avg_memory_usage_mb': avg_memory,
                'by_operation_type': operation_summaries,
                'generated_at': datetime.datetime.now().isoformat()
            }


# Utility functions for easy integration
def create_performance_aware_logger(base_logger, monitor_interval: int = 30):
    """
    Create a performance-aware logger that adapts to system load.

    Args:
        base_logger: Base logger to wrap
        monitor_interval: Performance monitoring interval in seconds

    Returns:
        AdaptiveLogger instance
    """
    monitor = PerformanceMonitor(check_interval=monitor_interval)
    monitor.start_monitoring()

    adaptive_logger = AdaptiveLogger(base_logger, monitor)
    return adaptive_logger


def get_system_performance_summary() -> Dict[str, Any]:
    """
    Get a quick summary of current system performance.

    Returns:
        Performance summary dictionary
    """
    monitor = PerformanceMonitor()
    metrics = monitor._collect_system_metrics()
    load = monitor._calculate_system_load(metrics)

    return {
        'timestamp': metrics.timestamp.isoformat(),
        'system_load': load.value,
        'cpu_percent': metrics.cpu_percent,
        'memory_percent': metrics.memory_percent,
        'disk_usage_percent': metrics.disk_usage_percent,
        'psutil_available': HAS_PSUTIL
    }