# Monthly Backup Path Structure Update

## Overview

The TNGD monthly backup system has been updated to use the new configurable OSS path structure consistently across all backup types. This ensures that monthly backups, daily backups, and single table backups all follow the same organized path template.

## Monthly Backup Architecture

### Monthly Mode
```
run_monthly_backup.bat [month] [year]
├── For each day in month:
│   └── call run_daily_backup.bat
│       └── DailyBackupScheduler
│           └── UnifiedTableProcessor
│               └── ConfigManager.get_oss_path()
└── Result: Files stored in Devo/{month}/week {n}/{date}/
```

### Historical Mode
```
run_monthly_backup.bat historical [start-date] [end-date]
└── HistoricalBackupProcessor
    └── For each date in range:
        └── UnifiedTableProcessor
            └── ConfigManager.get_oss_path()
└── Result: Files stored in Devo/{month}/week {n}/{date}/
```

## Path Structure Changes

### Previous Structure (Deprecated)
Monthly backups would create files in the daily backup structure:
```
backup/daily/2025-06-01/table_name_timestamp.tar.gz
backup/daily/2025-06-02/table_name_timestamp.tar.gz
...
backup/daily/2025-06-30/table_name_timestamp.tar.gz
```

### New Structure (Current)
Monthly backups now use the configurable template:
```
Devo/June/week 1/2025-06-01/table_name_timestamp_2025-06-01.tar.gz
Devo/June/week 1/2025-06-02/table_name_timestamp_2025-06-02.tar.gz
...
Devo/June/week 5/2025-06-30/table_name_timestamp_2025-06-30.tar.gz
```

## Monthly Backup Examples

### June 2025 Monthly Backup
```bash
# Run full month backup
run_monthly_backup.bat june 2025

# Expected file structure:
Devo/June/week 1/2025-06-01/my_app_tngd_waf_20250601_120000_2025-06-01.tar.gz
Devo/June/week 1/2025-06-07/my_app_tngd_waf_20250607_120000_2025-06-07.tar.gz
Devo/June/week 2/2025-06-08/my_app_tngd_waf_20250608_120000_2025-06-08.tar.gz
Devo/June/week 2/2025-06-14/my_app_tngd_waf_20250614_120000_2025-06-14.tar.gz
Devo/June/week 3/2025-06-15/my_app_tngd_waf_20250615_120000_2025-06-15.tar.gz
Devo/June/week 3/2025-06-19/my_app_tngd_waf_20250619_120000_2025-06-19.tar.gz
Devo/June/week 4/2025-06-22/my_app_tngd_waf_20250622_120000_2025-06-22.tar.gz
Devo/June/week 4/2025-06-28/my_app_tngd_waf_20250628_120000_2025-06-28.tar.gz
Devo/June/week 5/2025-06-29/my_app_tngd_waf_20250629_120000_2025-06-29.tar.gz
```

### Historical Date Range Backup
```bash
# Run historical backup for specific date range
run_monthly_backup.bat historical 2025-03-01 2025-03-31

# Expected file structure:
Devo/March/week 1/2025-03-01/my_app_tngd_waf_20250301_120000_2025-03-01.tar.gz
Devo/March/week 1/2025-03-07/my_app_tngd_waf_20250307_120000_2025-03-07.tar.gz
Devo/March/week 2/2025-03-08/my_app_tngd_waf_20250308_120000_2025-03-08.tar.gz
...
Devo/March/week 5/2025-03-31/my_app_tngd_waf_20250331_120000_2025-03-31.tar.gz
```

## Configuration

The path structure is controlled by the same configuration used for all backup types:

```json
{
  "storage": {
    "oss_path_template": "Devo/{month_name_str}/week {week_number}/{date_str}/{table_name}_{date_str}.tar.gz"
  }
}
```

### Template Variables
- `{month_name_str}` - Full month name (January, February, March, etc.)
- `{week_number}` - Week number within the month (1-5)
- `{date_str}` - Date in YYYY-MM-DD format
- `{table_name}` - Table name with timestamp for uniqueness

## Benefits for Monthly Backups

### 1. **Improved Organization**
- Monthly backups are organized by month and week
- Easy to locate specific date ranges
- Consistent with daily and single table backups

### 2. **Better Navigation**
- OSS console navigation is more intuitive
- Files grouped logically by time periods
- Reduced clutter in directory listings

### 3. **Enhanced Scalability**
- Better performance with hierarchical structure
- Supports long-term retention policies
- Easier automated cleanup and archival

### 4. **Operational Consistency**
- All backup types use the same path structure
- Unified monitoring and management
- Consistent documentation and procedures

## OSS Console Navigation

To find monthly backup files in the OSS console:

1. **Navigate to bucket**: `siem-security-logs`
2. **Go to directory**: `Devo/`
3. **Select month**: `June/`, `March/`, etc.
4. **Select week**: `week 1/`, `week 2/`, etc.
5. **Select date**: `2025-06-19/`, etc.
6. **Find files**: All backup files for that date

## Backward Compatibility

- **Old monthly backups**: Remain accessible at `backup/daily/{date}/`
- **New monthly backups**: Stored at `Devo/{month}/week {n}/{date}/`
- **No data loss**: All existing files remain accessible
- **Gradual transition**: New backups use new structure automatically

## Testing and Validation

### Path Generation Test
```bash
python test_monthly_backup_path_simulation.py
```

### OSS Console Verification
```bash
python test_oss_connection.py
```

### Dry-Run Testing
```bash
# Test monthly backup without actual execution
run_monthly_backup.bat june 2025 --dry-run

# Test historical backup without actual execution
run_monthly_backup.bat historical 2025-06-01 2025-06-30 --dry-run
```

## Troubleshooting

### Common Issues

1. **Files not found in expected location**
   - Check you're looking in `Devo/` not `backup/daily/`
   - Verify the correct month and week directories

2. **Path generation errors**
   - Ensure `config.json` has correct `oss_path_template`
   - Check ConfigManager initialization

3. **Monthly backup failures**
   - Check disk space availability
   - Verify table configuration in `tabletest/tables.json`
   - Review logs in `logs/monthly/` directory

### Verification Steps

1. **Check configuration**:
   ```bash
   python test_oss_path_generation.py
   ```

2. **Verify OSS connectivity**:
   ```bash
   python test_oss_connection.py
   ```

3. **Test monthly backup simulation**:
   ```bash
   python test_monthly_backup_path_simulation.py
   ```

## Implementation Status

- ✅ **Monthly backup script**: Updated with new path documentation
- ✅ **Historical backup processor**: Uses UnifiedTableProcessor with new paths
- ✅ **Daily backup scheduler**: Integrated with new path structure
- ✅ **Configuration**: Template properly configured
- ✅ **Testing**: Comprehensive test suite available
- ✅ **Documentation**: Updated with new path structure
- ✅ **Backward compatibility**: Maintained for existing files

## Summary

The monthly backup system now consistently uses the configurable OSS path structure `Devo/{month_name_str}/week {week_number}/{date_str}/` for all backup operations. This provides:

- **Consistency** across all backup types
- **Better organization** of backup files
- **Improved scalability** for long-term storage
- **Enhanced navigation** in OSS console
- **Backward compatibility** with existing files

All monthly backup operations (monthly mode and historical mode) automatically use the new path structure without requiring any configuration changes.

---

**Last Updated**: June 19, 2025  
**Version**: 1.0  
**Status**: Active
