#!/usr/bin/env python3
"""
Incremental Validation Engine for TNGD Backup System

This module implements incremental validation with checksum-based change detection
and validation caching to reduce redundant validation operations.

Features:
- Checksum-based change detection for tables
- Smart validation caching with TTL (Time To Live)
- Incremental validation that skips unchanged data
- Validation result persistence and retrieval
- Performance metrics and monitoring
- Configurable validation intervals and thresholds

Author: TNGD Backup System
Date: 2025-06-23
"""

import hashlib
import json
import time
import sqlite3
import threading
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from pathlib import Path

from core.backup_config import BackupConfig
from core.devo_client import DevoClient
from utils.enhanced_logging import get_logger

logger = get_logger(__name__)


@dataclass
class ValidationCacheEntry:
    """Represents a validation cache entry."""
    table_name: str
    checksum: str
    last_validated: datetime
    validation_result: Dict[str, Any]
    row_count: int
    data_size_bytes: int
    ttl_seconds: int = 3600  # Default 1 hour TTL
    
    def is_expired(self) -> bool:
        """Check if the cache entry has expired."""
        return datetime.now() > self.last_validated + timedelta(seconds=self.ttl_seconds)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        data = asdict(self)
        data['last_validated'] = self.last_validated.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ValidationCacheEntry':
        """Create from dictionary."""
        data['last_validated'] = datetime.fromisoformat(data['last_validated'])
        return cls(**data)


class ValidationCache:
    """Persistent validation cache with SQLite backend."""
    
    def __init__(self, cache_dir: str = "cache"):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        self.db_path = self.cache_dir / "validation_cache.db"
        self._lock = threading.Lock()
        self._init_database()
    
    def _init_database(self):
        """Initialize the SQLite database."""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS validation_cache (
                    table_name TEXT PRIMARY KEY,
                    checksum TEXT NOT NULL,
                    last_validated TEXT NOT NULL,
                    validation_result TEXT NOT NULL,
                    row_count INTEGER NOT NULL,
                    data_size_bytes INTEGER NOT NULL,
                    ttl_seconds INTEGER NOT NULL
                )
            """)
            conn.commit()
    
    def get(self, table_name: str) -> Optional[ValidationCacheEntry]:
        """Get validation cache entry for a table."""
        with self._lock:
            try:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.execute(
                        "SELECT * FROM validation_cache WHERE table_name = ?",
                        (table_name,)
                    )
                    row = cursor.fetchone()
                    
                    if row:
                        entry = ValidationCacheEntry(
                            table_name=row[0],
                            checksum=row[1],
                            last_validated=datetime.fromisoformat(row[2]),
                            validation_result=json.loads(row[3]),
                            row_count=row[4],
                            data_size_bytes=row[5],
                            ttl_seconds=row[6]
                        )
                        
                        # Check if expired
                        if entry.is_expired():
                            self.remove(table_name)
                            return None
                        
                        return entry
                        
            except Exception as e:
                logger.error(f"Error retrieving cache entry for {table_name}: {str(e)}")
                
        return None
    
    def put(self, entry: ValidationCacheEntry):
        """Store validation cache entry."""
        with self._lock:
            try:
                with sqlite3.connect(self.db_path) as conn:
                    conn.execute("""
                        INSERT OR REPLACE INTO validation_cache 
                        (table_name, checksum, last_validated, validation_result, 
                         row_count, data_size_bytes, ttl_seconds)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    """, (
                        entry.table_name,
                        entry.checksum,
                        entry.last_validated.isoformat(),
                        json.dumps(entry.validation_result),
                        entry.row_count,
                        entry.data_size_bytes,
                        entry.ttl_seconds
                    ))
                    conn.commit()
                    
            except Exception as e:
                logger.error(f"Error storing cache entry for {entry.table_name}: {str(e)}")
    
    def remove(self, table_name: str):
        """Remove validation cache entry."""
        with self._lock:
            try:
                with sqlite3.connect(self.db_path) as conn:
                    conn.execute("DELETE FROM validation_cache WHERE table_name = ?", (table_name,))
                    conn.commit()
                    
            except Exception as e:
                logger.error(f"Error removing cache entry for {table_name}: {str(e)}")
    
    def cleanup_expired(self):
        """Remove all expired cache entries."""
        with self._lock:
            try:
                with sqlite3.connect(self.db_path) as conn:
                    # Get all entries and check expiration
                    cursor = conn.execute("SELECT table_name, last_validated, ttl_seconds FROM validation_cache")
                    expired_tables = []
                    
                    for row in cursor.fetchall():
                        last_validated = datetime.fromisoformat(row[1])
                        ttl_seconds = row[2]
                        if datetime.now() > last_validated + timedelta(seconds=ttl_seconds):
                            expired_tables.append(row[0])
                    
                    # Remove expired entries
                    for table_name in expired_tables:
                        conn.execute("DELETE FROM validation_cache WHERE table_name = ?", (table_name,))
                    
                    conn.commit()
                    
                    if expired_tables:
                        logger.info(f"Cleaned up {len(expired_tables)} expired validation cache entries")
                        
            except Exception as e:
                logger.error(f"Error cleaning up expired cache entries: {str(e)}")


class TableChecksumCalculator:
    """Calculates checksums for tables to detect changes."""
    
    def __init__(self, devo_client: DevoClient):
        self.devo_client = devo_client
    
    def calculate_table_checksum(self, table_name: str, config: BackupConfig) -> Tuple[str, int, int]:
        """
        Calculate checksum for a table based on metadata and sample data.
        
        Args:
            table_name: Name of the table
            config: Backup configuration
            
        Returns:
            Tuple of (checksum, row_count, estimated_size_bytes)
        """
        try:
            # Build date filter
            if config.specific_date:
                date_str = config.specific_date.strftime('%Y-%m-%d')
                where_clause = f"where eventdate >= '{date_str} 00:00:00' and eventdate < '{date_str} 23:59:59'"
            else:
                where_clause = f"where eventdate >= now() - {config.days} * day()"
            
            # Get row count and basic statistics
            count_query = f"from {table_name} {where_clause} select count() as total_rows"
            # Use configurable timeout for count queries
            count_timeout = 120  # Default count query timeout
            if hasattr(config, 'config_manager') and config.config_manager:
                count_timeout = config.config_manager.get('backup', 'query_timeouts', {}).get('count_query', 120)

            count_result = self.devo_client.execute_query(
                count_query,
                days=config.days,
                timeout=count_timeout,
                table_name=table_name
            )
            
            row_count = count_result[0].get('total_rows', 0) if count_result else 0
            
            if row_count == 0:
                # Empty table - use table name as checksum
                checksum = hashlib.md5(table_name.encode()).hexdigest()
                return checksum, 0, 0
            
            # Sample data for checksum calculation (first and last few rows)
            sample_query = f"from {table_name} {where_clause} select * limit 10"
            sample_result = self.devo_client.execute_query(
                sample_query,
                days=config.days,
                timeout=count_timeout,  # Use same timeout as count query
                table_name=table_name
            )
            
            # Create checksum from table metadata and sample data
            checksum_data = {
                'table_name': table_name,
                'row_count': row_count,
                'sample_data': sample_result[:5] if sample_result else [],  # First 5 rows
                'date_range': where_clause
            }
            
            # Calculate MD5 checksum
            checksum_str = json.dumps(checksum_data, sort_keys=True, default=str)
            checksum = hashlib.md5(checksum_str.encode()).hexdigest()
            
            # Estimate data size (rough calculation)
            estimated_size = row_count * 1024 if row_count > 0 else 0  # 1KB per row estimate
            
            return checksum, row_count, estimated_size
            
        except Exception as e:
            logger.error(f"Error calculating checksum for {table_name}: {str(e)}")
            # Return a fallback checksum based on table name and timestamp
            fallback_checksum = hashlib.md5(f"{table_name}_{int(time.time())}".encode()).hexdigest()
            return fallback_checksum, 0, 0


class IncrementalValidator:
    """
    Incremental validation engine with caching and change detection.
    """
    
    def __init__(self, config: BackupConfig):
        self.config = config
        self.cache = ValidationCache()
        self.devo_client = DevoClient()
        self.checksum_calculator = TableChecksumCalculator(self.devo_client)
        
        # Performance settings
        self.validation_ttl = config.config_manager.get('validation', 'cache_ttl_seconds', 3600)
        self.skip_recent_validations = config.config_manager.get('validation', 'skip_recent', True)
        self.force_validation_threshold = config.config_manager.get('validation', 'force_threshold_hours', 24)
        
        logger.info(f"Incremental validator initialized with TTL: {self.validation_ttl}s")
    
    def validate_tables(self, table_names: List[str]) -> Dict[str, Any]:
        """
        Validate tables using incremental approach.
        
        Args:
            table_names: List of table names to validate
            
        Returns:
            Validation results with performance metrics
        """
        start_time = time.time()
        
        results = {
            'total_tables': len(table_names),
            'validated_tables': 0,
            'cached_results': 0,
            'skipped_tables': 0,
            'failed_validations': 0,
            'table_results': {},
            'performance_metrics': {}
        }
        
        # Cleanup expired cache entries
        self.cache.cleanup_expired()
        
        for table_name in table_names:
            try:
                result = self._validate_single_table(table_name)
                results['table_results'][table_name] = result
                
                if result['status'] == 'cached':
                    results['cached_results'] += 1
                elif result['status'] == 'validated':
                    results['validated_tables'] += 1
                elif result['status'] == 'skipped':
                    results['skipped_tables'] += 1
                else:
                    results['failed_validations'] += 1
                    
            except Exception as e:
                logger.error(f"Error validating table {table_name}: {str(e)}")
                results['table_results'][table_name] = {
                    'status': 'error',
                    'error': str(e)
                }
                results['failed_validations'] += 1
        
        # Calculate performance metrics
        total_duration = time.time() - start_time
        results['performance_metrics'] = {
            'total_duration': total_duration,
            'tables_per_second': len(table_names) / total_duration if total_duration > 0 else 0,
            'cache_hit_rate': results['cached_results'] / len(table_names) if table_names else 0,
            'validation_efficiency': (results['cached_results'] + results['skipped_tables']) / len(table_names) if table_names else 0
        }
        
        logger.info(f"Incremental validation completed: {results['cached_results']} cached, "
                   f"{results['validated_tables']} validated, {results['skipped_tables']} skipped, "
                   f"{results['failed_validations']} failed in {total_duration:.2f}s")
        
        return results
    
    def _validate_single_table(self, table_name: str) -> Dict[str, Any]:
        """Validate a single table using incremental approach."""
        
        # Calculate current checksum
        current_checksum, row_count, data_size = self.checksum_calculator.calculate_table_checksum(
            table_name, self.config
        )
        
        # Check cache
        cached_entry = self.cache.get(table_name)
        
        if cached_entry and not cached_entry.is_expired():
            # Check if data has changed
            if cached_entry.checksum == current_checksum:
                logger.debug(f"Table {table_name} unchanged, using cached validation result")
                return {
                    'status': 'cached',
                    'checksum': current_checksum,
                    'row_count': row_count,
                    'last_validated': cached_entry.last_validated.isoformat(),
                    'validation_result': cached_entry.validation_result
                }
        
        # Perform actual validation
        logger.info(f"Performing validation for table {table_name}")
        validation_result = self._perform_table_validation(table_name, row_count)
        
        # Cache the result
        cache_entry = ValidationCacheEntry(
            table_name=table_name,
            checksum=current_checksum,
            last_validated=datetime.now(),
            validation_result=validation_result,
            row_count=row_count,
            data_size_bytes=data_size,
            ttl_seconds=self.validation_ttl
        )
        
        self.cache.put(cache_entry)
        
        return {
            'status': 'validated',
            'checksum': current_checksum,
            'row_count': row_count,
            'validation_result': validation_result
        }
    
    def _perform_table_validation(self, table_name: str, row_count: int) -> Dict[str, Any]:
        """Perform actual table validation."""
        try:
            # Basic validation checks
            validation_result = {
                'accessible': True,
                'has_data': row_count > 0,
                'row_count': row_count,
                'validation_time': datetime.now().isoformat(),
                'checks_passed': []
            }
            
            # Add more validation checks as needed
            if row_count > 0:
                validation_result['checks_passed'].append('data_present')
            
            validation_result['checks_passed'].append('table_accessible')
            
            return validation_result
            
        except Exception as e:
            return {
                'accessible': False,
                'has_data': False,
                'error': str(e),
                'validation_time': datetime.now().isoformat()
            }
