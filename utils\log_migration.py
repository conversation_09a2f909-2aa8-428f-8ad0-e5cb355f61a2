#!/usr/bin/env python3
"""
Log Migration Utility for TNGD Backup System

This utility migrates existing logs to the new hierarchical structure
and sets up the enhanced logging system.

Usage:
    python -m utils.log_migration --migrate
    python -m utils.log_migration --setup-structure
    python -m utils.log_migration --cleanup-old
"""

import shutil
import argparse
import datetime
import re
from pathlib import Path
from typing import List, Tuple, Optional

from utils.enhanced_logging import LogDirectoryManager, OperationType


class LogMigrator:
    """Migrates existing logs to the new hierarchical structure."""

    def __init__(self, source_dir: str = "logs", backup_dir: str = "logs_backup"):
        self.source_dir = Path(source_dir)
        self.backup_dir = Path(backup_dir)
        self.directory_manager = LogDirectoryManager(str(self.source_dir))

    def backup_existing_logs(self) -> bool:
        """Create a backup of existing logs before migration."""
        if not self.source_dir.exists():
            print("No existing logs directory found.")
            return True

        try:
            if self.backup_dir.exists():
                print(f"Backup directory {self.backup_dir} already exists.")
                response = input("Overwrite? (y/N): ").lower()
                if response != 'y':
                    return False
                shutil.rmtree(self.backup_dir)

            shutil.copytree(self.source_dir, self.backup_dir)
            print(f"✓ Created backup at {self.backup_dir}")
            return True

        except Exception as e:
            print(f"✗ Failed to create backup: {e}")
            return False

    def analyze_existing_logs(self) -> List[Tuple[Path, str, Optional[datetime.datetime]]]:
        """Analyze existing log files and categorize them."""
        log_files = []

        if not self.source_dir.exists():
            return log_files

        # Pattern to extract dates from log filenames
        date_pattern = re.compile(r'(\d{4}-\d{2}-\d{2})')

        for file_path in self.source_dir.rglob("*.log"):
            if file_path.is_file():
                filename = file_path.name

                # Extract date from filename
                date_match = date_pattern.search(filename)
                log_date = None
                if date_match:
                    try:
                        log_date = datetime.datetime.strptime(date_match.group(1), "%Y-%m-%d")
                    except ValueError:
                        pass

                # Categorize log file
                category = self._categorize_log_file(filename, file_path)
                log_files.append((file_path, category, log_date))

        return log_files

    def _categorize_log_file(self, filename: str, file_path: Path) -> str:
        """Categorize a log file based on its name and location."""
        filename_lower = filename.lower()
        path_str = str(file_path).lower()

        # Check for specific patterns
        if "daily" in path_str or "daily_backup" in filename_lower:
            return "daily_backup"
        elif "monthly" in path_str or "monthly_backup" in filename_lower:
            return "monthly_backup"
        elif "historical" in path_str or "historical" in filename_lower:
            return "historical_backup"
        elif "performance" in path_str or "performance" in filename_lower:
            return "performance"
        elif "security" in path_str or "security" in filename_lower:
            return "security"
        elif "error" in filename_lower:
            return "errors"
        elif "backup" in filename_lower:
            return "daily_backup"  # Default backup logs to daily
        else:
            return "system"

    def migrate_logs(self, dry_run: bool = False) -> bool:
        """Migrate existing logs to the new structure."""
        log_files = self.analyze_existing_logs()

        if not log_files:
            print("No log files found to migrate.")
            return True

        print(f"Found {len(log_files)} log files to migrate:")

        migration_plan = []
        for file_path, category, log_date in log_files:
            new_path = self._get_new_path(category, file_path.name, log_date)
            migration_plan.append((file_path, new_path, category))
            print(f"  {file_path} -> {new_path} ({category})")

        if dry_run:
            print("\nDry run completed. Use --migrate to perform actual migration.")
            return True

        # Confirm migration
        response = input(f"\nProceed with migration of {len(migration_plan)} files? (y/N): ").lower()
        if response != 'y':
            print("Migration cancelled.")
            return False

        # Perform migration
        success_count = 0
        for source_path, dest_path, category in migration_plan:
            try:
                # Ensure destination directory exists
                dest_path.parent.mkdir(parents=True, exist_ok=True)

                # Move the file
                shutil.move(str(source_path), str(dest_path))
                success_count += 1
                print(f"✓ Migrated {source_path.name}")

            except Exception as e:
                print(f"✗ Failed to migrate {source_path.name}: {e}")

        print(f"\nMigration completed: {success_count}/{len(migration_plan)} files migrated successfully.")
        return success_count == len(migration_plan)

    def _get_new_path(self, category: str, filename: str, log_date: Optional[datetime.datetime]) -> Path:
        """Get the new path for a log file based on category and date."""
        if log_date is None:
            log_date = datetime.datetime.now()

        # Map categories to operation types
        operation_mapping = {
            "daily_backup": OperationType.DAILY_BACKUP,
            "monthly_backup": OperationType.MONTHLY_BACKUP,
            "historical_backup": OperationType.HISTORICAL_BACKUP,
            "performance": OperationType.SYSTEM_MAINTENANCE,
            "security": OperationType.SYSTEM_MAINTENANCE,
            "errors": OperationType.DAILY_BACKUP,
            "system": OperationType.SYSTEM_MAINTENANCE
        }

        operation_type = operation_mapping.get(category, OperationType.SYSTEM_MAINTENANCE)

        # Determine log type from filename
        if "error" in filename.lower():
            log_type = "errors"
        elif "performance" in filename.lower():
            log_type = "performance"
        elif "security" in filename.lower():
            log_type = "security"
        else:
            log_type = "operations"

        return self.directory_manager.get_log_path(operation_type, log_type, log_date)

    def setup_new_structure(self) -> bool:
        """Set up the new log directory structure."""
        try:
            self.directory_manager._ensure_directory_structure()
            print("✓ New log directory structure created successfully.")

            # Create sample configuration file
            self._create_sample_config()
            return True

        except Exception as e:
            print(f"✗ Failed to create directory structure: {e}")
            return False

    def _create_sample_config(self):
        """Create a sample logging configuration file."""
        config = {
            "logging": {
                "level": "INFO",
                "console_level": "INFO",
                "file_level": "DEBUG",
                "structured_format": False,
                "correlation_tracking": True,
                "security": {
                    "filter_sensitive_data": True,
                    "mask_file_paths": True,
                    "enable_integrity_check": False
                },
                "performance": {
                    "adaptive_logging": True,
                    "async_logging": False,
                    "buffer_size": 1000
                },
                "rotation": {
                    "max_file_size_mb": 50,
                    "max_files": 10,
                    "compress_archived": True,
                    "retention_days": 30
                },
                "outputs": {
                    "console": True,
                    "file": True,
                    "structured_file": False,
                    "syslog": False
                }
            }
        }

        config_path = Path("logging_config.json")
        if not config_path.exists():
            with open(config_path, 'w') as f:
                import json
                json.dump(config, f, indent=2)
            print(f"✓ Created sample logging configuration: {config_path}")

    def cleanup_old_structure(self, confirm: bool = False) -> bool:
        """Clean up old log files after successful migration."""
        if not confirm:
            print("This will remove old log files that have been migrated.")
            response = input("Are you sure? (y/N): ").lower()
            if response != 'y':
                print("Cleanup cancelled.")
                return False

        try:
            # Remove old flat log files in root logs directory
            old_files = list(self.source_dir.glob("*.log"))
            for file_path in old_files:
                if file_path.is_file():
                    file_path.unlink()
                    print(f"✓ Removed {file_path.name}")

            print(f"✓ Cleanup completed: {len(old_files)} old files removed.")
            return True

        except Exception as e:
            print(f"✗ Cleanup failed: {e}")
            return False


def main():
    """Main function for the log migration utility."""
    parser = argparse.ArgumentParser(description="TNGD Log Migration Utility")
    parser.add_argument("--migrate", action="store_true", help="Migrate existing logs to new structure")
    parser.add_argument("--dry-run", action="store_true", help="Show migration plan without executing")
    parser.add_argument("--setup-structure", action="store_true", help="Set up new log directory structure")
    parser.add_argument("--cleanup-old", action="store_true", help="Clean up old log files")
    parser.add_argument("--backup", action="store_true", help="Create backup before migration")
    parser.add_argument("--source-dir", default="logs", help="Source logs directory (default: logs)")
    parser.add_argument("--backup-dir", default="logs_backup", help="Backup directory (default: logs_backup)")

    args = parser.parse_args()

    migrator = LogMigrator(args.source_dir, args.backup_dir)

    if args.setup_structure:
        print("Setting up new log directory structure...")
        migrator.setup_new_structure()

    if args.backup:
        print("Creating backup of existing logs...")
        migrator.backup_existing_logs()

    if args.migrate or args.dry_run:
        print("Analyzing existing logs...")
        migrator.migrate_logs(dry_run=args.dry_run)

    if args.cleanup_old:
        print("Cleaning up old log structure...")
        migrator.cleanup_old_structure()


if __name__ == "__main__":
    main()