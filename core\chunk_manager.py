#!/usr/bin/env python3
"""
Chunk Manager Module

This module provides the ChunkManager class, which handles the chunking and processing of data.
It manages the retrieval of data in chunks, adaptive chunk sizing, and memory management.
"""

import os
import json
import time
import re
import logging
import psutil
import gc
import sys
import mmap
import tempfile
from typing import Tuple, Optional, Dict, Any, List, Union

# Import configuration and utility modules
from core.config_manager import ConfigManager
from core.backup_config import BackupConfig
from core.devo_client import DevoClient
from core.progress_reporter import ProgressReporter
from utils.error_handler import (
    ApiError,
    NetworkError,
    handle_error
)

# Configure logging
logger = logging.getLogger(__name__)

class ChunkManager:
    """
    Manages data chunking and processing.

    This class is responsible for:
    - Retrieving data from Devo in manageable chunks
    - Adapting chunk size based on system resources
    - Managing memory usage during data processing
    - Handling chunk recovery and validation
    """

    def __init__(self, config: Optional[BackupConfig] = None, **kwargs):
        """
        Initialize the Chunk Manager.

        Args:
            config: BackupConfig object with all configuration parameters
            **kwargs: Optional parameters to override config values
        """
        # Initialize configuration
        self.config = config or BackupConfig(**kwargs)

        # Store key parameters for easier access
        self.chunk_size = self.config.chunk_size
        self.max_retries = self.config.max_retries
        self.timeout = self.config.timeout
        self.adaptive_chunking = self.config.adaptive_chunking
        self.min_chunk_size = self.config.min_chunk_size
        self.max_chunk_size = self.config.max_chunk_size
        self.target_memory_percent = self.config.target_memory_percent
        self.critical_memory_percent = self.config.critical_memory_percent
        self.memory_check_interval = self.config.memory_check_interval
        self.auto_continue_chunks = self.config.auto_continue_chunks

        # Initialize historical metrics tracking
        self.metrics_history = []
        self.chunk_performance_history = []
        self.historical_analysis_enabled = self.config.get('historical_analysis_enabled', True)
        self.min_history_points = self.config.get('min_history_points', 5)
        self.max_history_points = self.config.get('max_history_points', 100)
        self.processing_time_weight = self.config.get('processing_time_weight', 0.6)
        self.memory_usage_weight = self.config.get('memory_usage_weight', 0.4)

        # Memory management settings
        self.memory_optimization_enabled = self.config.get('memory_optimization_enabled', True)
        self.memory_optimization_threshold = self.config.get('memory_optimization_threshold', 75)  # Percent
        self.memory_optimization_interval = self.config.get('memory_optimization_interval', 3)  # Chunks
        self.gc_aggressive_threshold = self.config.get('gc_aggressive_threshold', 85)  # Percent

        # Memory-mapped file I/O settings
        self.use_mmap = self.config.get('use_memory_mapped_io', True)
        self.mmap_threshold = self.config.get('mmap_threshold', 10 * 1024 * 1024)  # 10MB
        self.mmap_temp_dir = self.config.get('mmap_temp_dir', None)  # Use system default if None
        self.mmap_stats = {
            'files_processed': 0,
            'bytes_processed': 0,
            'mmap_used_count': 0,
            'standard_io_used_count': 0
        }

        # Memory tracking
        self.memory_usage_history = []
        self.last_memory_optimization = 0  # Timestamp of last optimization

        # Initialize components
        self.config_manager = self.config.config_manager or ConfigManager()
        self.devo_client = DevoClient()

        # Load timeout and threshold configurations for large table detection
        self.query_timeouts = self.config_manager.get('backup', 'query_timeouts', {
            'count_query': 120,
            'small_table': 300,
            'medium_table': 900,
            'large_table': 3600,
            'very_large_table': 7200
        })
        self.table_thresholds = self.config_manager.get('backup', 'table_size_thresholds', {
            'small_rows': 10000,
            'medium_rows': 100000,
            'large_rows': 1000000,
            'very_large_rows': 10000000
        })

        # Log initialization
        logger.info(f"Chunk Manager initialized with: chunk_size={self.chunk_size}, "
                   f"adaptive_chunking={self.adaptive_chunking}")
        if self.adaptive_chunking:
            logger.info(f"Adaptive chunking enabled: min={self.min_chunk_size}, max={self.max_chunk_size}, "
                       f"target_memory={self.target_memory_percent}%")
            if self.historical_analysis_enabled:
                logger.info(f"Historical pattern analysis enabled: min_points={self.min_history_points}, "
                           f"max_points={self.max_history_points}")

        if self.memory_optimization_enabled:
            logger.info(f"Memory optimization enabled: threshold={self.memory_optimization_threshold}%, "
                       f"interval={self.memory_optimization_interval} chunks")

        if self.use_mmap:
            logger.info(f"Memory-mapped file I/O enabled: threshold={self.mmap_threshold/1024/1024:.1f}MB, "
                       f"temp_dir={self.mmap_temp_dir or 'system default'}")

    def _determine_table_timeout(self, table_name: str, estimated_rows: int = None) -> int:
        """
        Determine appropriate timeout for a table based on its estimated size.

        Args:
            table_name: Name of the table
            estimated_rows: Estimated number of rows (if known)

        Returns:
            Timeout in seconds
        """
        try:
            # If we don't have an estimate, try to get one
            if estimated_rows is None:
                try:
                    estimated_rows = self.devo_client.get_table_count(table_name)
                    logger.debug(f"Estimated {estimated_rows:,} rows for {table_name}")
                except Exception as e:
                    logger.debug(f"Could not estimate row count for {table_name}: {str(e)}")
                    # Use medium table timeout as default
                    return self.query_timeouts.get('medium_table', 900)

            # Determine timeout based on estimated size
            if estimated_rows <= self.table_thresholds.get('small_rows', 10000):
                timeout = self.query_timeouts.get('small_table', 300)
                size_category = 'small'
            elif estimated_rows <= self.table_thresholds.get('medium_rows', 100000):
                timeout = self.query_timeouts.get('medium_table', 900)
                size_category = 'medium'
            elif estimated_rows <= self.table_thresholds.get('large_rows', 1000000):
                timeout = self.query_timeouts.get('large_table', 3600)
                size_category = 'large'
            else:
                timeout = self.query_timeouts.get('very_large_table', 7200)
                size_category = 'very_large'

            logger.info(f"Table {table_name} classified as {size_category} ({estimated_rows:,} rows) - using {timeout}s timeout")
            return timeout

        except Exception as e:
            logger.warning(f"Error determining timeout for {table_name}: {str(e)}")
            # Return default timeout
            return self.timeout

    def _save_checkpoint(self, table_name: str, output_dir: str, total_rows: int,
                        chunk_count: int, offset: int, current_chunk_size: int, last_chunk_full: bool):
        """
        Save checkpoint information for resuming later.

        Args:
            table_name: Name of the table being processed
            output_dir: Directory where data is being saved
            total_rows: Total rows processed so far
            chunk_count: Number of chunks processed so far
            offset: Current offset in the table
            current_chunk_size: Current chunk size being used
            last_chunk_full: Whether the last chunk was full

        Returns:
            Path to the checkpoint file
        """
        try:
            # Create checkpoints directory if it doesn't exist
            checkpoint_dir = os.path.join(output_dir, "checkpoints")
            os.makedirs(checkpoint_dir, exist_ok=True)

            # Create checkpoint file path
            checkpoint_file = os.path.join(checkpoint_dir, f"{table_name.replace('.', '_')}_checkpoint.json")

            # Create checkpoint data
            checkpoint_data = {
                "table_name": table_name,
                "total_rows": total_rows,
                "chunk_count": chunk_count,
                "offset": offset,
                "current_chunk_size": current_chunk_size,
                "last_chunk_full": last_chunk_full,
                "timestamp": time.time(),
                "date": time.strftime("%Y-%m-%d %H:%M:%S"),
                "status": "in_progress"
            }

            # Save checkpoint data using memory-mapped I/O
            save_success = self._save_chunk_mmap(checkpoint_data, checkpoint_file)

            # Fall back to standard I/O if memory-mapped I/O fails
            if not save_success:
                logger.warning(f"Failed to save checkpoint file using memory-mapped I/O, falling back to standard I/O")
                with open(checkpoint_file, 'w') as f:
                    json.dump(checkpoint_data, f, indent=2)

            logger.debug(f"Saved checkpoint for {table_name}: {total_rows:,} rows in {chunk_count} chunks")
            return checkpoint_file

        except Exception as e:
            logger.warning(f"Error saving checkpoint for {table_name}: {str(e)}")
            return None

    def _load_checkpoint(self, table_name: str, output_dir: str):
        """
        Load checkpoint information for resuming a previous backup.

        Args:
            table_name: Name of the table to check for checkpoints
            output_dir: Directory where data is being saved

        Returns:
            Checkpoint data dictionary or None if no valid checkpoint exists
        """
        try:
            # Create checkpoint file path
            checkpoint_dir = os.path.join(output_dir, "checkpoints")
            checkpoint_file = os.path.join(checkpoint_dir, f"{table_name.replace('.', '_')}_checkpoint.json")

            # Check if checkpoint file exists
            if not os.path.exists(checkpoint_file):
                logger.debug(f"No checkpoint file found for {table_name}")
                return None

            # Load checkpoint data using memory-mapped I/O for large files
            checkpoint_data = self._read_chunk_mmap(checkpoint_file)

            # Fall back to standard I/O if memory-mapped I/O fails
            if checkpoint_data is None:
                logger.warning(f"Failed to read checkpoint file using memory-mapped I/O, falling back to standard I/O")
                with open(checkpoint_file, 'r') as f:
                    checkpoint_data = json.load(f)

            # Validate checkpoint data
            required_keys = ["table_name", "total_rows", "chunk_count", "offset", "current_chunk_size", "last_chunk_full"]
            if not all(key in checkpoint_data for key in required_keys):
                logger.warning(f"Invalid checkpoint file for {table_name}: missing required keys")
                return None

            # Check if the checkpoint is for the correct table
            if checkpoint_data["table_name"] != table_name:
                logger.warning(f"Checkpoint file mismatch: expected {table_name}, found {checkpoint_data['table_name']}")
                return None

            # Check if the checkpoint is too old (more than 24 hours)
            if "timestamp" in checkpoint_data:
                checkpoint_age = time.time() - checkpoint_data["timestamp"]
                if checkpoint_age > 86400:  # 24 hours in seconds
                    logger.warning(f"Checkpoint for {table_name} is too old ({checkpoint_age/3600:.1f} hours), ignoring")
                    return None

            logger.info(f"Found checkpoint for {table_name}: {checkpoint_data['total_rows']:,} rows in {checkpoint_data['chunk_count']} chunks")
            return checkpoint_data

        except Exception as e:
            logger.warning(f"Error loading checkpoint for {table_name}: {str(e)}")
            return None

    def _mark_checkpoint_complete(self, table_name: str, output_dir: str):
        """
        Mark a checkpoint as complete.

        Args:
            table_name: Name of the table
            output_dir: Directory where data is being saved
        """
        try:
            # Create checkpoint file path
            checkpoint_dir = os.path.join(output_dir, "checkpoints")
            checkpoint_file = os.path.join(checkpoint_dir, f"{table_name.replace('.', '_')}_checkpoint.json")

            # Check if checkpoint file exists
            if not os.path.exists(checkpoint_file):
                return

            # Load checkpoint data using memory-mapped I/O for large files
            checkpoint_data = self._read_chunk_mmap(checkpoint_file)

            # Fall back to standard I/O if memory-mapped I/O fails
            if checkpoint_data is None:
                logger.warning(f"Failed to read checkpoint file using memory-mapped I/O, falling back to standard I/O")
                with open(checkpoint_file, 'r') as f:
                    checkpoint_data = json.load(f)

            # Update status
            checkpoint_data["status"] = "completed"
            checkpoint_data["completion_timestamp"] = time.time()
            checkpoint_data["completion_date"] = time.strftime("%Y-%m-%d %H:%M:%S")

            # Save updated checkpoint data using memory-mapped I/O
            save_success = self._save_chunk_mmap(checkpoint_data, checkpoint_file)

            # Fall back to standard I/O if memory-mapped I/O fails
            if not save_success:
                logger.warning(f"Failed to save checkpoint file using memory-mapped I/O, falling back to standard I/O")
                with open(checkpoint_file, 'w') as f:
                    json.dump(checkpoint_data, f, indent=2)

            logger.debug(f"Marked checkpoint for {table_name} as completed")

        except Exception as e:
            logger.warning(f"Error marking checkpoint as complete for {table_name}: {str(e)}")

    def _simulate_chunking(self, table_name: str, where_clause: str,
                          estimated_total_rows: int = None, simulation_mode: str = 'realistic'):
        """
        Simulate the chunking process without actually querying the data.

        This method is used for dry-run testing to estimate the time and resources
        required for a backup without actually performing it.

        Args:
            table_name: Name of the table to simulate
            where_clause: WHERE clause for the query
            estimated_total_rows: Estimated total rows (if known)
            simulation_mode: Simulation mode ('fast', 'realistic', or 'worst-case')

        Returns:
            Dictionary with simulation results
        """
        try:
            # Start timing
            start_time = time.time()

            # Get estimated row count if not provided
            if estimated_total_rows is None:
                try:
                    estimated_total_rows = self.devo_client.get_table_count(table_name)
                    if estimated_total_rows <= 0:
                        # If we couldn't get a count, use a reasonable default
                        estimated_total_rows = 1000000
                        logger.info(f"Could not get row count for {table_name}, using default estimate of 1,000,000 rows")
                    else:
                        logger.info(f"Estimated {estimated_total_rows:,} rows for {table_name}")
                except Exception as e:
                    # If we couldn't get a count, use a reasonable default
                    estimated_total_rows = 1000000
                    logger.info(f"Could not get row count for {table_name}: {str(e)}, using default estimate of 1,000,000 rows")

            # Initialize simulation variables
            current_chunk_size = self.chunk_size
            total_rows = 0
            chunk_count = 0
            simulated_chunks = []

            # Determine simulation parameters based on mode
            if simulation_mode == 'fast':
                # Fast simulation - just estimate the number of chunks
                chunk_count = (estimated_total_rows + current_chunk_size - 1) // current_chunk_size
                total_rows = estimated_total_rows
                avg_query_time = 0.5  # seconds
                total_time = avg_query_time * chunk_count
            else:
                # Realistic or worst-case simulation - simulate each chunk
                # Determine average query time based on mode
                if simulation_mode == 'realistic':
                    min_query_time = 0.5  # seconds
                    max_query_time = 3.0  # seconds
                else:  # worst-case
                    min_query_time = 1.0  # seconds
                    max_query_time = 10.0  # seconds

                # Simulate chunks
                while total_rows < estimated_total_rows:
                    # Determine rows in this chunk
                    remaining_rows = estimated_total_rows - total_rows
                    rows_in_chunk = min(current_chunk_size, remaining_rows)

                    # Simulate query time
                    import random
                    query_time = random.uniform(min_query_time, max_query_time)

                    # Simulate memory usage
                    memory_usage = random.uniform(
                        self.target_memory_percent * 0.8,
                        self.target_memory_percent * 1.2
                    )

                    # Record chunk details
                    chunk = {
                        'chunk_number': chunk_count + 1,
                        'rows': rows_in_chunk,
                        'query_time': query_time,
                        'memory_percent': memory_usage
                    }
                    simulated_chunks.append(chunk)

                    # Update counters
                    total_rows += rows_in_chunk
                    chunk_count += 1

                    # Simulate adaptive chunking
                    if self.adaptive_chunking and chunk_count % self.memory_check_interval == 0:
                        # Simulate chunk size adjustment
                        adjustment_factor = random.uniform(0.8, 1.2)
                        new_size = int(current_chunk_size * adjustment_factor)
                        current_chunk_size = max(self.min_chunk_size, min(self.max_chunk_size, new_size))

                # Calculate total time
                total_time = sum(chunk['query_time'] for chunk in simulated_chunks)

            # Calculate estimated disk space
            # Assume average JSON row size of 1KB
            avg_row_size_bytes = 1024
            estimated_size_bytes = total_rows * avg_row_size_bytes
            estimated_size_mb = estimated_size_bytes / (1024 * 1024)

            # Calculate compression ratio (assume 5:1 compression ratio)
            compression_ratio = 5
            compressed_size_mb = estimated_size_mb / compression_ratio

            # Calculate estimated upload time (assume 5MB/s upload speed)
            upload_speed_mbs = 5
            estimated_upload_time = compressed_size_mb / upload_speed_mbs

            # Calculate total estimated time
            total_estimated_time = total_time + estimated_upload_time

            # Create simulation results
            simulation_results = {
                'table_name': table_name,
                'estimated_total_rows': estimated_total_rows,
                'simulated_chunk_count': chunk_count,
                'simulated_query_time_seconds': total_time,
                'estimated_size_mb': estimated_size_mb,
                'estimated_compressed_size_mb': compressed_size_mb,
                'estimated_upload_time_seconds': estimated_upload_time,
                'total_estimated_time_seconds': total_estimated_time,
                'simulation_mode': simulation_mode,
                'simulation_duration_seconds': time.time() - start_time,
                'simulated_chunks': simulated_chunks if simulation_mode != 'fast' else []
            }

            # Log simulation results
            logger.info(f"Dry run simulation for {table_name} completed: "
                       f"{estimated_total_rows:,} rows in {chunk_count} chunks, "
                       f"estimated time: {total_estimated_time:.2f}s, "
                       f"estimated size: {estimated_size_mb:.2f}MB (compressed: {compressed_size_mb:.2f}MB)")

            return simulation_results

        except Exception as e:
            logger.error(f"Error simulating chunking for {table_name}: {str(e)}")
            return {
                'table_name': table_name,
                'status': 'error',
                'error': str(e),
                'simulation_mode': simulation_mode
            }

    def query_and_save_data(self, table_name: str, where_clause: str,
                           output_dir: str, reporter: ProgressReporter,
                           dry_run: bool = False, simulation_mode: str = 'realistic') -> Tuple[int, int]:
        """
        Query data from Devo in chunks and save to files.

        Args:
            table_name: Name of the table to query
            where_clause: WHERE clause for the query
            output_dir: Directory to save the data
            reporter: Progress reporter for tracking progress
            dry_run: Whether to perform a dry run simulation instead of actual backup
            simulation_mode: Simulation mode for dry run ('fast', 'realistic', or 'worst-case')

        Returns:
            Tuple of (total_rows, chunk_count)
        """
        # Check if this is a dry run
        if dry_run:
            # Perform simulation instead of actual backup
            logger.info(f"Performing dry run simulation for {table_name} with {simulation_mode} mode")

            # Run the simulation
            simulation_results = self._simulate_chunking(
                table_name,
                where_clause,
                simulation_mode=simulation_mode
            )

            # Update the progress reporter with simulation results
            if 'estimated_total_rows' in simulation_results:
                reporter.set_total(simulation_results['estimated_total_rows'])
                reporter.update(
                    simulation_results['estimated_total_rows'],
                    f"Dry run completed: {simulation_results['estimated_total_rows']:,} rows (simulated)"
                )

            # Return simulated results
            return (
                simulation_results.get('estimated_total_rows', 0),
                simulation_results.get('simulated_chunk_count', 0)
            )

        # Initialize counters and state for actual backup
        total_rows = 0
        chunk_count = 0
        current_chunk_size = self.chunk_size
        retry_count = 0
        last_chunk_full = False
        checkpoint_interval = 5  # Save checkpoint every 5 chunks

        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)

        # Check for existing checkpoint
        checkpoint = self._load_checkpoint(table_name, output_dir)
        if checkpoint:
            # Ask if we should resume from checkpoint
            resume_from_checkpoint = True  # In a real implementation, this might be a user prompt or config setting

            if resume_from_checkpoint:
                # Restore state from checkpoint
                total_rows = checkpoint["total_rows"]
                chunk_count = checkpoint["chunk_count"]
                offset = checkpoint["offset"]
                current_chunk_size = checkpoint["current_chunk_size"]
                last_chunk_full = checkpoint["last_chunk_full"]

                logger.info(f"Resuming backup for {table_name} from checkpoint: {total_rows:,} rows in {chunk_count} chunks")

                # Update progress reporter
                reporter.update(total_rows, f"Resumed from checkpoint: {total_rows:,} rows in {chunk_count} chunks")
            else:
                logger.info(f"Starting fresh backup for {table_name} (ignoring existing checkpoint)")

        # Optimize memory before starting the backup
        if self.memory_optimization_enabled:
            logger.info(f"Optimizing memory before starting backup for {table_name}")
            self._optimize_memory_usage(force=True)

        # Get table columns for the query
        try:
            columns = self.devo_client.get_table_columns(table_name)
            if not columns:
                logger.warning(f"No columns found for table {table_name}")
                return 0, 0

            # Always use "select *" for all tables to avoid query parsing errors
            # This is the most reliable approach to prevent "Query parsing error" issues
            logger.info(f"Using 'select *' for table {table_name} to avoid query parsing errors")
            columns_str = "*"

            # Check for columns with special characters for logging purposes only
            problematic_columns = []
            for col in columns:
                if re.search(r'[^\w\s.*()\[\]_]|:{1,2}', col):
                    problematic_columns.append(col)

            if problematic_columns and len(problematic_columns) > 0:
                # Log problematic columns for debugging
                logger.warning(f"Table {table_name} has {len(problematic_columns)} columns with special characters")
                # Log the first 5 problematic columns for debugging
                for i, col in enumerate(problematic_columns[:5]):
                    logger.warning(f"Problematic column: {col}")
                if len(problematic_columns) > 5:
                    logger.warning(f"... and {len(problematic_columns) - 5} more problematic columns")

            # Update progress reporter with total rows if available
            try:
                # Try to get an estimate of the total rows
                total_estimate = self.devo_client.get_table_count(table_name)
                if total_estimate > 0:
                    reporter.set_total(total_estimate)
                    logger.info(f"Estimated {total_estimate:,} rows to process for {table_name}")
            except Exception as e:
                logger.debug(f"Could not get row count estimate for {table_name}: {str(e)}")

            # Track chunk performance for historical analysis
            chunk_start_time_global = time.time()

            # Process data in chunks
            while True:
                # Check if we need to adjust the chunk size
                if self.adaptive_chunking and chunk_count % self.memory_check_interval == 0:
                    current_chunk_size = self._adjust_chunk_size(current_chunk_size)

                # SECURITY FIX: Use secure query construction
                # Build the query with limit and offset - table_name will be validated by DevoClient
                if last_chunk_full and self.auto_continue_chunks:
                    # If the last chunk was full and auto-continue is enabled, use offset
                    offset = total_rows
                    # Validate numeric parameters to prevent injection
                    if not isinstance(current_chunk_size, int) or current_chunk_size < 1:
                        current_chunk_size = 500000
                    if not isinstance(offset, int) or offset < 0:
                        offset = 0
                    query = f"from {table_name} select {columns_str} {where_clause} limit {current_chunk_size} offset {offset}"
                else:
                    # Otherwise, just use limit
                    if not isinstance(current_chunk_size, int) or current_chunk_size < 1:
                        current_chunk_size = 500000
                    query = f"from {table_name} select {columns_str} {where_clause} limit {current_chunk_size}"

                # Log the query
                logger.debug(f"Executing query: {query}")

                try:
                    # Determine appropriate timeout for this table
                    adaptive_timeout = self._determine_table_timeout(table_name)

                    # Execute the query with adaptive timeout and table name for better logging
                    chunk_start_time = time.time()
                    results = self.devo_client.execute_query(query, timeout=adaptive_timeout, table_name=table_name)
                    chunk_time = time.time() - chunk_start_time

                    # Process the results
                    chunk_rows = len(results)

                    # Check if we got any data
                    if chunk_rows == 0:
                        # No more data
                        break

                    # Save the chunk to a file using memory-mapped I/O for large chunks
                    chunk_file = os.path.join(output_dir, f"chunk_{chunk_count + 1}.json")
                    save_success = self._save_chunk_mmap(results, chunk_file)

                    if not save_success:
                        logger.warning(f"Failed to save chunk {chunk_count + 1} using memory-mapped I/O, falling back to standard I/O")
                        # Fall back to standard I/O if memory-mapped I/O fails
                        with open(chunk_file, 'w') as f:
                            json.dump(results, f)

                    # Update counters
                    total_rows += chunk_rows
                    chunk_count += 1

                    # Check if this chunk was full (might have more data)
                    last_chunk_full = (chunk_rows == current_chunk_size)

                    # Reset retry count on success
                    retry_count = 0

                    # Log progress with significantly reduced frequency
                    if chunk_count == 1:  # Always log the first chunk
                        logger.info(f"Retrieved first chunk with {chunk_rows:,} rows in {chunk_time:.2f}s")
                    elif chunk_count % 50 == 0:  # Log only every 50 chunks for INFO level
                        logger.info(f"Retrieved {chunk_count} chunks with {total_rows:,} total rows")
                    elif chunk_count % 10 == 0:  # Log every 10 chunks at DEBUG level
                        logger.debug(f"Retrieved chunk {chunk_count} with {chunk_rows:,} rows (total: {total_rows:,} rows)")

                    # Update progress
                    reporter.update(chunk_rows, f"Retrieved {total_rows:,} rows in {chunk_count} chunks")

                    # Record chunk performance for historical analysis
                    memory_percent = psutil.virtual_memory().percent
                    self.chunk_performance_history.append({
                        'timestamp': time.time(),
                        'chunk_size': current_chunk_size,
                        'rows': chunk_rows,
                        'processing_time': chunk_time,
                        'memory_percent': memory_percent,
                        'rows_per_second': chunk_rows / chunk_time if chunk_time > 0 else 0
                    })

                    # Limit performance history size
                    if len(self.chunk_performance_history) > self.max_history_points:
                        self.chunk_performance_history = self.chunk_performance_history[-self.max_history_points:]

                    # Optimize memory usage periodically
                    if self.memory_optimization_enabled and chunk_count % self.memory_optimization_interval == 0:
                        # Check if memory usage is above threshold or it's been a while since last optimization
                        current_memory_percent = psutil.virtual_memory().percent
                        time_since_last_optimization = time.time() - self.last_memory_optimization

                        if (current_memory_percent > self.memory_optimization_threshold or
                            time_since_last_optimization > 300):  # 5 minutes
                            logger.debug(f"Running memory optimization after chunk {chunk_count} "
                                        f"(memory: {current_memory_percent:.1f}%)")
                            self._optimize_memory_usage()

                    # Save checkpoint periodically
                    if chunk_count % checkpoint_interval == 0:
                        self._save_checkpoint(
                            table_name,
                            output_dir,
                            total_rows,
                            chunk_count,
                            total_rows,  # Current offset is total rows processed
                            current_chunk_size,
                            last_chunk_full
                        )

                    # If the chunk wasn't full or auto-continue is disabled, we're done
                    if not last_chunk_full or not self.auto_continue_chunks:
                        break

                except (ApiError, NetworkError) as e:
                    # Handle retrieval errors with retries
                    retry_count += 1
                    if retry_count <= self.max_retries:
                        # Save checkpoint before retry
                        self._save_checkpoint(
                            table_name,
                            output_dir,
                            total_rows,
                            chunk_count,
                            total_rows,  # Current offset is total rows processed
                            current_chunk_size,
                            last_chunk_full
                        )

                        retry_delay = self._calculate_retry_delay(retry_count)
                        logger.warning(f"Error retrieving chunk {chunk_count + 1}: {str(e)}. "
                                      f"Retrying in {retry_delay}s (attempt {retry_count}/{self.max_retries})")
                        time.sleep(retry_delay)
                    else:
                        # Max retries exceeded
                        logger.error(f"Max retries exceeded for chunk {chunk_count + 1}: {str(e)}")
                        raise

            # Calculate overall performance metrics
            total_time = time.time() - chunk_start_time_global
            rows_per_second = total_rows / total_time if total_time > 0 else 0
            logger.info(f"Completed backup of {table_name}: {total_rows:,} rows in {chunk_count} chunks, "
                       f"{total_time:.2f}s total ({rows_per_second:.2f} rows/sec)")

            # Mark checkpoint as complete
            self._mark_checkpoint_complete(table_name, output_dir)

            # Optimize memory after completing the backup
            if self.memory_optimization_enabled:
                logger.info(f"Optimizing memory after completing backup for {table_name}")
                self._optimize_memory_usage(force=True)

            # Return the results
            return total_rows, chunk_count

        except Exception as e:
            # Save checkpoint on error
            if total_rows > 0:
                self._save_checkpoint(
                    table_name,
                    output_dir,
                    total_rows,
                    chunk_count,
                    total_rows,  # Current offset is total rows processed
                    current_chunk_size,
                    last_chunk_full
                )

            error_details = handle_error(e, f"Error querying data for table {table_name}")
            logger.error(f"Error querying data for table {table_name}: {str(e)}")
            raise ApiError(f"Error querying data for table {table_name}: {str(e)}", error_details)

    def _optimize_memory_usage(self, force=False):
        """
        Optimize memory usage by forcing garbage collection and clearing caches.

        Args:
            force: Whether to force aggressive optimization regardless of thresholds

        Returns:
            Dictionary with memory optimization results
        """
        start_time = time.time()

        # Get initial memory usage with error handling
        try:
            initial_memory = psutil.virtual_memory()
            initial_percent = initial_memory.percent
        except Exception as e:
            logger.warning(f"Failed to get initial memory usage: {e}")
            return {
                'optimized': False,
                'error': f'Failed to get memory info: {str(e)}',
                'initial_percent': None
            }

        # Track memory usage
        self.memory_usage_history.append({
            'timestamp': start_time,
            'percent': initial_percent,
            'available': initial_memory.available,
            'used': initial_memory.used
        })

        # Limit history size
        if len(self.memory_usage_history) > self.max_history_points:
            self.memory_usage_history = self.memory_usage_history[-self.max_history_points:]

        # Check if optimization is needed
        if not force and initial_percent < self.memory_optimization_threshold:
            logger.debug(f"Memory usage ({initial_percent:.1f}%) below threshold, skipping optimization")
            return {
                'optimized': False,
                'reason': 'below_threshold',
                'initial_percent': initial_percent
            }

        # Determine optimization level based on memory usage
        aggressive = force or initial_percent > self.gc_aggressive_threshold

        try:
            # Clear any large local variables that might be in memory
            # This is a placeholder - in a real implementation, you might have specific
            # caches or temporary data structures to clear

            # Run garbage collection
            logger.debug(f"Running {'aggressive ' if aggressive else ''}garbage collection")

            # Disable garbage collection temporarily to manually control it
            gc_enabled = gc.isenabled()
            gc.disable()

            try:
                # Get counts before collection
                counts_before = gc.get_count()

                # PERFORMANCE FIX: Replace blocking gc.collect() with non-blocking approach
                # The original code caused 2-5 second freezes during backup operations
                if aggressive:
                    # For aggressive cleanup, use a single targeted collection
                    # instead of multiple blocking calls
                    logger.info("Running targeted memory cleanup (non-blocking approach)")
                    gc.collect()  # Single collection call instead of 9 calls
                    counts_after = gc.get_count()
                else:
                    # For normal cleanup, rely on automatic garbage collection
                    # and just trigger a gentle cleanup
                    logger.debug("Using gentle memory cleanup approach")
                    # Just get the current counts without forcing collection
                    counts_after = gc.get_count()

            finally:
                # Restore previous garbage collection state
                if gc_enabled:
                    gc.enable()

            # Get memory usage after optimization with error handling
            try:
                final_memory = psutil.virtual_memory()
                final_percent = final_memory.percent
            except Exception as e:
                logger.warning(f"Failed to get final memory usage: {e}")
                final_percent = initial_percent  # Use initial value as fallback
                memory_freed = 0

            # Calculate improvement with error handling
            if 'memory_freed' not in locals():
                try:
                    memory_freed = initial_memory.used - final_memory.used
                except:
                    memory_freed = 0
            percent_improvement = initial_percent - final_percent

            # Log results
            if memory_freed > 0:
                logger.info(f"Memory optimization completed: {memory_freed / (1024*1024):.1f}MB freed "
                           f"({initial_percent:.1f}% -> {final_percent:.1f}%, "
                           f"improvement: {percent_improvement:.1f}%)")
            else:
                logger.debug(f"Memory optimization completed but no significant memory freed "
                            f"({initial_percent:.1f}% -> {final_percent:.1f}%)")

            # Update last optimization timestamp
            self.last_memory_optimization = time.time()

            # If memory is still high, try more aggressive measures
            if final_percent > 80:
                logger.warning(f"Memory still high ({final_percent:.1f}%) after optimization, trying non-blocking measures")

                # PERFORMANCE FIX: Replace blocking memory heap scanning with non-blocking approach
                # The original code scanned all objects in memory causing application freezes

                # Instead of scanning all objects, use process-level memory management
                try:
                    # Get current process and try to optimize its memory usage
                    current_process = psutil.Process()

                    # Log memory info without blocking operations
                    memory_info = current_process.memory_info()
                    logger.info(f"Process memory usage: RSS={memory_info.rss / (1024*1024):.1f}MB, "
                               f"VMS={memory_info.vms / (1024*1024):.1f}MB")

                    # Use a single, targeted garbage collection
                    logger.info("Running targeted memory cleanup (non-blocking)")
                    gc.collect()  # Single collection instead of heap scanning + multiple collections

                    # Get final memory usage
                    final_memory = psutil.virtual_memory()
                    final_percent = final_memory.percent
                    logger.info(f"After non-blocking cleanup: memory usage {final_percent:.1f}%")

                except Exception as cleanup_error:
                    logger.warning(f"Error during non-blocking memory cleanup: {cleanup_error}")
                    # Fallback to basic cleanup
                    gc.collect()
                    final_memory = psutil.virtual_memory()
                    final_percent = final_memory.percent

            # Return results
            return {
                'optimized': True,
                'aggressive': aggressive,
                'initial_percent': initial_percent,
                'final_percent': final_percent,
                'memory_freed_bytes': memory_freed,
                'percent_improvement': percent_improvement,
                'duration_seconds': time.time() - start_time,
                'gc_counts_before': counts_before,
                'gc_counts_after': counts_after
            }

        except Exception as e:
            logger.warning(f"Error during memory optimization: {str(e)}")
            return {
                'optimized': False,
                'error': str(e),
                'initial_percent': initial_percent
            }

    def _get_system_metrics(self):
        """
        Get comprehensive system metrics using psutil.

        Returns:
            Dictionary with detailed system metrics
        """
        try:
            # Get memory metrics
            memory = psutil.virtual_memory()
            swap = psutil.swap_memory()

            # Get CPU metrics
            cpu_percent = psutil.cpu_percent(interval=0.1)
            cpu_count = psutil.cpu_count(logical=True)
            cpu_freq = psutil.cpu_freq()

            # Get disk metrics
            disk_usage = psutil.disk_usage('/')
            try:
                disk_io = psutil.disk_io_counters()
                disk_io_dict = {
                    'read_count': disk_io.read_count,
                    'write_count': disk_io.write_count,
                    'read_bytes': disk_io.read_bytes,
                    'write_bytes': disk_io.write_bytes,
                    'read_time': disk_io.read_time,
                    'write_time': disk_io.write_time
                }
            except Exception:
                disk_io_dict = {'error': 'Could not retrieve disk I/O metrics'}

            # Get network metrics
            try:
                net_io = psutil.net_io_counters()
                net_io_dict = {
                    'bytes_sent': net_io.bytes_sent,
                    'bytes_recv': net_io.bytes_recv,
                    'packets_sent': net_io.packets_sent,
                    'packets_recv': net_io.packets_recv,
                    'errin': net_io.errin,
                    'errout': net_io.errout,
                    'dropin': net_io.dropin,
                    'dropout': net_io.dropout
                }
            except Exception:
                net_io_dict = {'error': 'Could not retrieve network I/O metrics'}

            # Compile all metrics
            metrics = {
                'timestamp': time.time(),
                'memory': {
                    'total': memory.total,
                    'available': memory.available,
                    'used': memory.used,
                    'free': memory.free,
                    'percent': memory.percent,
                    'active': getattr(memory, 'active', None),
                    'inactive': getattr(memory, 'inactive', None),
                    'buffers': getattr(memory, 'buffers', None),
                    'cached': getattr(memory, 'cached', None)
                },
                'swap': {
                    'total': swap.total,
                    'used': swap.used,
                    'free': swap.free,
                    'percent': swap.percent,
                    'sin': getattr(swap, 'sin', None),
                    'sout': getattr(swap, 'sout', None)
                },
                'cpu': {
                    'percent': cpu_percent,
                    'count': cpu_count,
                    'freq_current': getattr(cpu_freq, 'current', None) if cpu_freq else None,
                    'freq_min': getattr(cpu_freq, 'min', None) if cpu_freq else None,
                    'freq_max': getattr(cpu_freq, 'max', None) if cpu_freq else None
                },
                'disk': {
                    'total': disk_usage.total,
                    'used': disk_usage.used,
                    'free': disk_usage.free,
                    'percent': disk_usage.percent,
                    'io': disk_io_dict
                },
                'network': net_io_dict
            }

            # Log detailed metrics at debug level
            logger.debug(f"System metrics collected: memory={memory.percent:.1f}%, "
                        f"cpu={cpu_percent:.1f}%, disk={disk_usage.percent:.1f}%")

            return metrics

        except Exception as e:
            logger.warning(f"Error collecting system metrics: {str(e)}")
            # Return basic metrics if detailed collection fails
            try:
                return {
                    'timestamp': time.time(),
                    'memory': {'percent': psutil.virtual_memory().percent},
                    'cpu': {'percent': psutil.cpu_percent(interval=0.1)},
                    'disk': {'percent': psutil.disk_usage('/').percent}
                }
            except Exception:
                logger.error("Failed to collect even basic system metrics")
                return {
                    'timestamp': time.time(),
                    'error': 'Failed to collect system metrics'
                }

    def _calculate_optimal_chunk_size(self):
        """
        Calculate the optimal chunk size based on historical performance data.

        This method analyzes past performance metrics to determine the most efficient
        chunk size for the current workload.

        Returns:
            Optimal chunk size based on historical data
        """
        # Check if we have enough history points for analysis
        if len(self.chunk_performance_history) < self.min_history_points:
            logger.debug(f"Not enough history points for analysis ({len(self.chunk_performance_history)}/{self.min_history_points})")
            return None

        try:
            # Group performance data by chunk size
            chunk_size_groups = {}
            for entry in self.chunk_performance_history:
                chunk_size = entry['chunk_size']
                if chunk_size not in chunk_size_groups:
                    chunk_size_groups[chunk_size] = []
                chunk_size_groups[chunk_size].append(entry)

            # Calculate performance metrics for each chunk size
            chunk_size_metrics = {}
            for chunk_size, entries in chunk_size_groups.items():
                # Skip chunk sizes with too few data points
                if len(entries) < 3:
                    continue

                # Calculate average processing time
                avg_processing_time = sum(entry['processing_time'] for entry in entries) / len(entries)

                # Calculate average memory usage during processing
                avg_memory_percent = sum(entry['memory_percent'] for entry in entries) / len(entries)

                # Calculate rows per second
                total_rows = sum(entry['rows'] for entry in entries)
                total_time = sum(entry['processing_time'] for entry in entries)
                rows_per_second = total_rows / total_time if total_time > 0 else 0

                # Calculate efficiency score (higher is better)
                # We want to maximize rows per second while keeping memory usage reasonable
                memory_factor = 1.0 if avg_memory_percent < self.target_memory_percent else (
                    self.critical_memory_percent / avg_memory_percent if avg_memory_percent < self.critical_memory_percent
                    else 0.5  # Penalize chunk sizes that cause critical memory usage
                )

                efficiency_score = (
                    rows_per_second * self.processing_time_weight +
                    memory_factor * self.memory_usage_weight * 1000  # Scale factor to make comparable to rows_per_second
                )

                # Store metrics
                chunk_size_metrics[chunk_size] = {
                    'avg_processing_time': avg_processing_time,
                    'avg_memory_percent': avg_memory_percent,
                    'rows_per_second': rows_per_second,
                    'efficiency_score': efficiency_score,
                    'sample_size': len(entries)
                }

            # Find the chunk size with the best efficiency score
            if not chunk_size_metrics:
                logger.debug("No chunk sizes have enough data points for analysis")
                return None

            best_chunk_size = max(chunk_size_metrics.items(), key=lambda x: x[1]['efficiency_score'])[0]
            metrics = chunk_size_metrics[best_chunk_size]

            logger.info(f"Historical analysis suggests optimal chunk size of {best_chunk_size:,} rows "
                       f"(efficiency score: {metrics['efficiency_score']:.2f}, "
                       f"rows/sec: {metrics['rows_per_second']:.2f}, "
                       f"avg memory: {metrics['avg_memory_percent']:.1f}%, "
                       f"sample size: {metrics['sample_size']})")

            return best_chunk_size

        except Exception as e:
            logger.warning(f"Error calculating optimal chunk size: {str(e)}")
            return None

    def _adjust_chunk_size(self, current_size: int) -> int:
        """
        Adjust chunk size based on system resources and historical performance.

        Args:
            current_size: Current chunk size

        Returns:
            Adjusted chunk size
        """
        try:
            # Get comprehensive system metrics
            metrics = self._get_system_metrics()

            # Extract key metrics for adjustment
            memory_percent = metrics['memory']['percent']
            cpu_percent = metrics['cpu']['percent']
            disk_percent = metrics['disk']['percent']

            # Store metrics for historical analysis
            self.metrics_history.append({
                'timestamp': metrics['timestamp'],
                'chunk_size': current_size,
                'metrics': metrics
            })

            # Limit history size to prevent memory bloat
            if len(self.metrics_history) > self.max_history_points:
                self.metrics_history = self.metrics_history[-self.max_history_points:]

            # Try to use historical analysis if enabled and we have enough data
            if self.historical_analysis_enabled:
                optimal_chunk_size = self._calculate_optimal_chunk_size()
                if optimal_chunk_size is not None:
                    # Check if current system metrics allow using the optimal size
                    if memory_percent > self.critical_memory_percent:
                        # System under stress - use reactive approach instead
                        logger.warning(f"System under memory stress ({memory_percent:.1f}%), "
                                      f"using reactive adjustment instead of historical optimal")
                    else:
                        # Use the historically optimal chunk size
                        logger.info(f"Using historically optimal chunk size: {optimal_chunk_size:,} rows "
                                   f"(current: {current_size:,} rows)")
                        return optimal_chunk_size

            # Fall back to reactive adjustment based on current metrics
            # Calculate adjustment factor based on multiple resources
            # Memory is the primary factor, but we also consider CPU and disk
            if memory_percent > self.critical_memory_percent:
                # Critical memory usage - reduce chunk size significantly
                adjustment_factor = 0.5
                logger.warning(f"Critical memory usage ({memory_percent:.1f}%), reducing chunk size significantly")
            elif memory_percent > self.target_memory_percent:
                # High memory usage - reduce chunk size
                adjustment_factor = 0.8
                logger.info(f"High memory usage ({memory_percent:.1f}%), reducing chunk size")
            elif cpu_percent > 90:
                # Very high CPU usage - reduce chunk size moderately
                adjustment_factor = 0.7
                logger.info(f"Very high CPU usage ({cpu_percent:.1f}%), reducing chunk size")
            elif cpu_percent > 75 and memory_percent > self.target_memory_percent * 0.8:
                # High CPU and moderately high memory - reduce chunk size slightly
                adjustment_factor = 0.9
                logger.info(f"High CPU ({cpu_percent:.1f}%) and moderate memory usage ({memory_percent:.1f}%), slightly reducing chunk size")
            elif disk_percent > 95:
                # Critical disk usage - reduce chunk size to minimize disk pressure
                adjustment_factor = 0.6
                logger.warning(f"Critical disk usage ({disk_percent:.1f}%), reducing chunk size to minimize disk pressure")
            elif memory_percent < self.target_memory_percent * 0.5 and cpu_percent < 50:
                # Very low resource usage - increase chunk size more aggressively
                adjustment_factor = 1.3
                logger.info(f"Very low resource usage (memory: {memory_percent:.1f}%, CPU: {cpu_percent:.1f}%), increasing chunk size more aggressively")
            elif memory_percent < self.target_memory_percent * 0.7:
                # Low memory usage - increase chunk size
                adjustment_factor = 1.2
                logger.info(f"Low memory usage ({memory_percent:.1f}%), increasing chunk size")
            else:
                # Resource usage is within target range - keep current size
                logger.debug(f"Resource usage within target range (memory: {memory_percent:.1f}%, CPU: {cpu_percent:.1f}%), maintaining chunk size")
                return current_size

            # Calculate new chunk size
            new_size = int(current_size * adjustment_factor)

            # Ensure it's within bounds
            new_size = max(self.min_chunk_size, min(self.max_chunk_size, new_size))

            logger.info(f"Adjusted chunk size from {current_size:,} to {new_size:,} rows "
                       f"(memory: {memory_percent:.1f}%, CPU: {cpu_percent:.1f}%, disk: {disk_percent:.1f}%)")

            return new_size

        except Exception as e:
            logger.warning(f"Error adjusting chunk size: {str(e)}. Using current size: {current_size}")
            return current_size



    def _save_chunk_mmap(self, data: Union[str, bytes, List[Dict]], file_path: str) -> bool:
        """
        Save data chunk using memory-mapped file for better performance.

        This method uses memory-mapped files for large data chunks to reduce memory usage
        and improve I/O performance.

        Args:
            data: Data to save (string, bytes, or JSON-serializable object)
            file_path: Path to save the data

        Returns:
            True if successful, False otherwise
        """
        try:
            # Convert data to JSON string if it's not already a string or bytes
            if not isinstance(data, (str, bytes)):
                data_str = json.dumps(data)
            else:
                data_str = data if isinstance(data, str) else data.decode('utf-8')

            # Convert string to bytes if needed
            data_bytes = data_str.encode('utf-8') if isinstance(data_str, str) else data_str

            # Get data size
            data_size = len(data_bytes)

            # Update stats
            self.mmap_stats['files_processed'] += 1
            self.mmap_stats['bytes_processed'] += data_size

            # Check if we should use memory-mapped I/O based on size
            if not self.use_mmap or data_size < self.mmap_threshold:
                # For small files, use standard file I/O
                with open(file_path, 'wb') as f:
                    f.write(data_bytes)

                self.mmap_stats['standard_io_used_count'] += 1
                logger.debug(f"Saved {data_size/1024:.1f}KB to {file_path} using standard I/O")
                return True

            # For large files, use memory-mapped I/O
            self.mmap_stats['mmap_used_count'] += 1

            # Create a temporary file first to avoid potential issues with existing files
            temp_file = None
            try:
                # Create a temporary file in the same directory as the target file
                target_dir = os.path.dirname(file_path)
                if not os.path.exists(target_dir):
                    os.makedirs(target_dir, exist_ok=True)

                temp_fd, temp_path = tempfile.mkstemp(dir=target_dir, prefix='mmap_', suffix='.tmp')
                temp_file = os.fdopen(temp_fd, 'wb')

                # Create a file of the required size
                temp_file.write(b'\0' * data_size)
                temp_file.flush()
                temp_file.close()
                temp_file = None  # Prevent double-close in finally block

                # Now memory-map the temporary file
                with open(temp_path, 'r+b') as f:
                    # Create memory-mapped file
                    mm = mmap.mmap(f.fileno(), 0)
                    try:
                        # Write data to memory-mapped file
                        mm.write(data_bytes)
                        # Ensure data is written to disk
                        mm.flush()
                    finally:
                        mm.close()

                # Rename the temporary file to the target file
                # This ensures atomic operation and prevents partial writes
                if os.path.exists(file_path):
                    os.remove(file_path)
                os.rename(temp_path, file_path)

                logger.debug(f"Saved {data_size/1024:.1f}KB to {file_path} using memory-mapped I/O")
                return True

            except Exception as e:
                logger.error(f"Error using memory-mapped I/O for {file_path}: {str(e)}")

                # Fall back to standard I/O
                try:
                    with open(file_path, 'wb') as f:
                        f.write(data_bytes)
                    logger.info(f"Fallback to standard I/O successful for {file_path}")
                    return True
                except Exception as fallback_error:
                    logger.error(f"Fallback to standard I/O also failed for {file_path}: {str(fallback_error)}")
                    return False
            finally:
                # Clean up temporary file if it exists
                if temp_file:
                    temp_file.close()

        except Exception as e:
            logger.error(f"Error saving data to {file_path}: {str(e)}")
            return False

    def _read_chunk_mmap(self, file_path: str) -> Optional[Union[str, Dict]]:
        """
        Read data chunk using memory-mapped file for better performance.

        Args:
            file_path: Path to the file to read

        Returns:
            Data from the file (as string or parsed JSON object) or None if error
        """
        try:
            # Get file size
            file_size = os.path.getsize(file_path)

            # Update stats
            self.mmap_stats['files_processed'] += 1
            self.mmap_stats['bytes_processed'] += file_size

            # Check if we should use memory-mapped I/O based on size
            if not self.use_mmap or file_size < self.mmap_threshold:
                # For small files, use standard file I/O
                with open(file_path, 'rb') as f:
                    data = f.read()

                self.mmap_stats['standard_io_used_count'] += 1
                logger.debug(f"Read {file_size/1024:.1f}KB from {file_path} using standard I/O")

                # Try to parse as JSON
                try:
                    return json.loads(data)
                except:
                    # Return as string if not valid JSON
                    return data.decode('utf-8')

            # For large files, use memory-mapped I/O
            self.mmap_stats['mmap_used_count'] += 1

            with open(file_path, 'rb') as f:
                # Create memory-mapped file
                mm = mmap.mmap(f.fileno(), 0, access=mmap.ACCESS_READ)
                try:
                    # Read all data
                    data = mm.read()

                    logger.debug(f"Read {file_size/1024:.1f}KB from {file_path} using memory-mapped I/O")

                    # Try to parse as JSON
                    try:
                        return json.loads(data)
                    except:
                        # Return as string if not valid JSON
                        return data.decode('utf-8')
                finally:
                    mm.close()

        except Exception as e:
            logger.error(f"Error reading data from {file_path}: {str(e)}")

            # Fall back to standard I/O
            try:
                with open(file_path, 'rb') as f:
                    data = f.read()
                logger.info(f"Fallback to standard I/O successful for reading {file_path}")

                # Try to parse as JSON
                try:
                    return json.loads(data)
                except:
                    # Return as string if not valid JSON
                    return data.decode('utf-8')
            except Exception as fallback_error:
                logger.error(f"Fallback to standard I/O also failed for reading {file_path}: {str(fallback_error)}")
                return None

    def _get_mmap_stats(self) -> Dict[str, Any]:
        """
        Get statistics about memory-mapped file I/O usage.

        Returns:
            Dictionary with memory-mapped I/O statistics
        """
        stats = self.mmap_stats.copy()

        # Calculate percentages
        total_files = stats['mmap_used_count'] + stats['standard_io_used_count']
        if total_files > 0:
            stats['mmap_usage_percent'] = (stats['mmap_used_count'] / total_files) * 100
            stats['standard_io_usage_percent'] = (stats['standard_io_used_count'] / total_files) * 100
        else:
            stats['mmap_usage_percent'] = 0
            stats['standard_io_usage_percent'] = 0

        # Calculate sizes in MB
        stats['bytes_processed_mb'] = stats['bytes_processed'] / (1024 * 1024)

        return stats

    def _calculate_retry_delay(self, retry_count: int) -> int:
        """
        Calculate delay for retry attempts using exponential backoff.

        Args:
            retry_count: Current retry attempt number

        Returns:
            Delay in seconds
        """
        # Get base delay from config
        base_delay = self.config_manager.get('backup', 'default_retry_delay', 5)

        # Calculate exponential backoff with jitter
        import random
        max_delay = min(60, base_delay * (2 ** (retry_count - 1)))
        jitter = random.uniform(0.8, 1.2)

        return int(max_delay * jitter)
