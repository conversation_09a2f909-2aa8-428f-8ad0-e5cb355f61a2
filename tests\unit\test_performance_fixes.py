#!/usr/bin/env python3
"""
Test Performance Anti-pattern Fixes

This module tests that performance anti-patterns have been fixed,
specifically the blocking gc.collect() calls that caused application freezes.
"""

import unittest
import time
import os
import sys
from unittest.mock import patch, MagicMock
import threading

# Add project root to Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from core.chunk_manager import ChunkManager
from core.config_manager import ConfigManager
from core.backup_config import BackupConfig


class TestPerformanceAntiPatternFixes(unittest.TestCase):
    """Test that performance anti-patterns have been fixed."""

    def setUp(self):
        """Set up test fixtures."""
        self.backup_config = BackupConfig()

    def test_memory_optimization_non_blocking(self):
        """Test that memory optimization doesn't cause long blocking operations."""
        # Create a chunk manager
        chunk_manager = ChunkManager(self.backup_config)
        
        # Mock psutil to simulate high memory usage
        with patch('psutil.virtual_memory') as mock_memory, \
             patch('psutil.Process') as mock_process, \
             patch('gc.collect') as mock_gc_collect:
            
            # Set up mocks to simulate high memory usage
            mock_memory.return_value.percent = 85.0  # High memory usage
            mock_memory.return_value.used = 8 * 1024 * 1024 * 1024  # 8GB
            mock_memory.return_value.available = 2 * 1024 * 1024 * 1024  # 2GB
            
            mock_process_instance = MagicMock()
            mock_process_instance.memory_info.return_value.rss = 1024 * 1024 * 1024  # 1GB
            mock_process_instance.memory_info.return_value.vms = 2 * 1024 * 1024 * 1024  # 2GB
            mock_process.return_value = mock_process_instance
            
            # Measure time for memory optimization
            start_time = time.time()
            
            # Call memory optimization (should be non-blocking now)
            result = chunk_manager._optimize_memory_usage(force=True)
            
            end_time = time.time()
            duration = end_time - start_time
            
            # Verify that the operation completed quickly (< 1 second)
            # The original blocking version could take 2-5 seconds
            self.assertLess(duration, 1.0, 
                           f"Memory optimization took {duration:.2f}s, should be < 1.0s")
            
            # Verify that gc.collect was called but not excessively
            # Original code called gc.collect() 9 times (3 generations × 3 passes)
            # New code should call it much less
            self.assertLessEqual(mock_gc_collect.call_count, 3,
                               f"gc.collect() called {mock_gc_collect.call_count} times, should be ≤ 3")
            
            # Verify that the function returned successfully
            self.assertIsInstance(result, dict)
            self.assertTrue(result.get('optimized', False))

    def test_no_blocking_heap_scanning(self):
        """Test that memory optimization doesn't scan the entire heap."""
        chunk_manager = ChunkManager(self.backup_config)
        
        # Mock gc.get_objects to detect if it's called (heap scanning)
        with patch('gc.get_objects') as mock_get_objects, \
             patch('psutil.virtual_memory') as mock_memory, \
             patch('psutil.Process') as mock_process:
            
            # Set up mocks
            mock_memory.return_value.percent = 85.0
            mock_memory.return_value.used = 8 * 1024 * 1024 * 1024
            mock_memory.return_value.available = 2 * 1024 * 1024 * 1024
            
            mock_process_instance = MagicMock()
            mock_process_instance.memory_info.return_value.rss = 1024 * 1024 * 1024
            mock_process_instance.memory_info.return_value.vms = 2 * 1024 * 1024 * 1024
            mock_process.return_value = mock_process_instance
            
            # Call memory optimization
            chunk_manager._optimize_memory_usage(force=True)
            
            # Verify that gc.get_objects() was NOT called
            # The original code called this to scan all objects in memory
            mock_get_objects.assert_not_called()

    def test_memory_optimization_performance_under_load(self):
        """Test memory optimization performance under simulated load."""
        chunk_manager = ChunkManager(self.backup_config)
        
        # Simulate concurrent operations
        results = []
        errors = []
        
        def memory_optimization_worker():
            """Worker function to test concurrent memory optimization."""
            try:
                with patch('psutil.virtual_memory') as mock_memory, \
                     patch('psutil.Process') as mock_process:
                    
                    mock_memory.return_value.percent = 75.0
                    mock_memory.return_value.used = 6 * 1024 * 1024 * 1024
                    mock_memory.return_value.available = 2 * 1024 * 1024 * 1024
                    
                    mock_process_instance = MagicMock()
                    mock_process_instance.memory_info.return_value.rss = 512 * 1024 * 1024
                    mock_process_instance.memory_info.return_value.vms = 1024 * 1024 * 1024
                    mock_process.return_value = mock_process_instance
                    
                    start_time = time.time()
                    worker_chunk_manager = ChunkManager(BackupConfig())
                    result = worker_chunk_manager._optimize_memory_usage()
                    end_time = time.time()
                    
                    results.append({
                        'duration': end_time - start_time,
                        'result': result
                    })
            except Exception as e:
                errors.append(str(e))
        
        # Run multiple concurrent memory optimizations
        threads = []
        for _ in range(3):
            thread = threading.Thread(target=memory_optimization_worker)
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join(timeout=5.0)  # 5 second timeout
        
        # Verify no errors occurred
        self.assertEqual(len(errors), 0, f"Errors occurred: {errors}")
        
        # Verify all operations completed quickly
        self.assertEqual(len(results), 3, "Not all operations completed")
        
        for i, result in enumerate(results):
            self.assertLess(result['duration'], 1.0,
                           f"Operation {i} took {result['duration']:.2f}s, should be < 1.0s")

    def test_memory_optimization_graceful_degradation(self):
        """Test that memory optimization degrades gracefully when psutil fails."""
        chunk_manager = ChunkManager(self.backup_config)
        
        # Mock psutil to raise exceptions
        with patch('psutil.virtual_memory', side_effect=Exception("psutil error")), \
             patch('psutil.Process', side_effect=Exception("process error")):
            
            # Memory optimization should still work and not crash
            start_time = time.time()
            result = chunk_manager._optimize_memory_usage()
            end_time = time.time()
            
            # Should complete quickly even with errors
            self.assertLess(end_time - start_time, 1.0)
            
            # Should return a result indicating what happened
            self.assertIsInstance(result, dict)

    def test_chunk_manager_memory_settings(self):
        """Test that chunk manager has reasonable memory optimization settings."""
        chunk_manager = ChunkManager(self.backup_config)
        
        # Verify that memory optimization is enabled but not too aggressive
        self.assertTrue(hasattr(chunk_manager, 'memory_optimization_enabled'))
        self.assertTrue(hasattr(chunk_manager, 'memory_optimization_threshold'))
        self.assertTrue(hasattr(chunk_manager, 'memory_optimization_interval'))
        
        # Verify reasonable thresholds
        if hasattr(chunk_manager, 'memory_optimization_threshold'):
            # Should not be too low (causing excessive optimization)
            # or too high (not optimizing when needed)
            threshold = chunk_manager.memory_optimization_threshold
            self.assertGreaterEqual(threshold, 60, "Memory threshold too low")
            self.assertLessEqual(threshold, 90, "Memory threshold too high")

    def test_no_excessive_gc_calls_during_normal_operation(self):
        """Test that normal operations don't trigger excessive garbage collection."""
        chunk_manager = ChunkManager(self.backup_config)
        
        with patch('gc.collect') as mock_gc_collect, \
             patch('psutil.virtual_memory') as mock_memory:
            
            # Set up normal memory usage (below optimization threshold)
            mock_memory.return_value.percent = 50.0
            mock_memory.return_value.used = 4 * 1024 * 1024 * 1024
            mock_memory.return_value.available = 4 * 1024 * 1024 * 1024
            
            # Call memory optimization with normal memory usage
            result = chunk_manager._optimize_memory_usage()
            
            # Should not trigger garbage collection for normal memory usage
            self.assertEqual(mock_gc_collect.call_count, 0,
                           "gc.collect() should not be called for normal memory usage")
            
            # Should return indicating no optimization was needed
            self.assertFalse(result.get('optimized', True))


if __name__ == '__main__':
    unittest.main(verbosity=2)
