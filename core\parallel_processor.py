#!/usr/bin/env python3
"""
Parallel Processing Engine for TNGD Backup System

This module implements selective parallelization for table backup operations
with configurable concurrency limits, resource monitoring, and error handling.

Features:
- Configurable thread pool with adaptive sizing
- Resource-aware concurrency control
- Independent table processing with isolation
- Comprehensive error handling and recovery
- Performance monitoring and metrics collection
- Graceful degradation under resource constraints

Author: TNGD Backup System
Date: 2025-06-23
"""

import threading
import time
import queue
import concurrent.futures
from typing import List, Dict, Any, Optional, Callable
from dataclasses import dataclass
from enum import Enum
import psutil
import logging

from core.backup_config import BackupConfig
from core.unified_table_processor import UnifiedTableProcessor
from core.performance_monitor import PerformanceMonitor
from utils.enhanced_logging import get_logger

logger = get_logger(__name__)


class ProcessingMode(Enum):
    """Processing mode for parallel operations."""
    SEQUENTIAL = "sequential"
    PARALLEL = "parallel"
    ADAPTIVE = "adaptive"


@dataclass
class ParallelTask:
    """Represents a parallel processing task."""
    table_name: str
    priority: int = 0
    estimated_duration: float = 0.0
    memory_requirement: float = 0.0
    dependencies: List[str] = None
    
    def __post_init__(self):
        if self.dependencies is None:
            self.dependencies = []


@dataclass
class ProcessingResult:
    """Result of parallel processing operation."""
    table_name: str
    status: str
    duration: float
    rows_processed: int = 0
    error: Optional[str] = None
    memory_used: float = 0.0
    thread_id: str = ""


class ResourceMonitor:
    """Monitors system resources for adaptive concurrency control."""
    
    def __init__(self, config: BackupConfig):
        self.config = config
        self.cpu_threshold = 80.0  # CPU usage threshold
        self.memory_threshold = 85.0  # Memory usage threshold
        self.monitoring_interval = 5.0  # seconds
        self._stop_monitoring = threading.Event()
        self._monitor_thread = None
        
    def start_monitoring(self):
        """Start resource monitoring thread."""
        if self._monitor_thread is None or not self._monitor_thread.is_alive():
            self._stop_monitoring.clear()
            self._monitor_thread = threading.Thread(target=self._monitor_resources)
            self._monitor_thread.daemon = True
            self._monitor_thread.start()
            logger.info("Resource monitoring started")
    
    def stop_monitoring(self):
        """Stop resource monitoring thread."""
        self._stop_monitoring.set()
        if self._monitor_thread:
            self._monitor_thread.join(timeout=2.0)
        logger.info("Resource monitoring stopped")
    
    def _monitor_resources(self):
        """Monitor system resources continuously."""
        while not self._stop_monitoring.wait(self.monitoring_interval):
            try:
                cpu_percent = psutil.cpu_percent(interval=1)
                memory_percent = psutil.virtual_memory().percent
                
                logger.debug(f"Resource usage - CPU: {cpu_percent:.1f}%, Memory: {memory_percent:.1f}%")
                
                # Log warnings if thresholds exceeded
                if cpu_percent > self.cpu_threshold:
                    logger.warning(f"High CPU usage detected: {cpu_percent:.1f}%")
                if memory_percent > self.memory_threshold:
                    logger.warning(f"High memory usage detected: {memory_percent:.1f}%")
                    
            except Exception as e:
                logger.error(f"Error monitoring resources: {str(e)}")
    
    def get_current_usage(self) -> Dict[str, float]:
        """Get current resource usage."""
        try:
            return {
                'cpu_percent': psutil.cpu_percent(interval=0.1),
                'memory_percent': psutil.virtual_memory().percent,
                'disk_io_percent': psutil.disk_io_counters().read_bytes if psutil.disk_io_counters() else 0
            }
        except Exception as e:
            logger.error(f"Error getting resource usage: {str(e)}")
            return {'cpu_percent': 0, 'memory_percent': 0, 'disk_io_percent': 0}
    
    def should_reduce_concurrency(self) -> bool:
        """Check if concurrency should be reduced due to resource constraints."""
        usage = self.get_current_usage()
        return (usage['cpu_percent'] > self.cpu_threshold or 
                usage['memory_percent'] > self.memory_threshold)


class AdaptiveThreadPool:
    """Adaptive thread pool that adjusts size based on system resources."""
    
    def __init__(self, config: BackupConfig, resource_monitor: ResourceMonitor):
        self.config = config
        self.resource_monitor = resource_monitor
        self.min_workers = max(1, config.max_threads // 4)
        self.max_workers = config.max_threads
        self.current_workers = self.min_workers
        self.executor = None
        self._adjustment_interval = 30.0  # seconds
        self._last_adjustment = time.time()
        
    def _should_adjust_pool_size(self) -> Optional[int]:
        """Determine if pool size should be adjusted."""
        now = time.time()
        if now - self._last_adjustment < self._adjustment_interval:
            return None
            
        usage = self.resource_monitor.get_current_usage()
        
        # Reduce workers if resources are constrained
        if self.resource_monitor.should_reduce_concurrency():
            if self.current_workers > self.min_workers:
                new_size = max(self.min_workers, self.current_workers - 1)
                logger.info(f"Reducing thread pool size to {new_size} due to resource constraints")
                return new_size
        
        # Increase workers if resources are available
        elif (usage['cpu_percent'] < 50 and usage['memory_percent'] < 60 and 
              self.current_workers < self.max_workers):
            new_size = min(self.max_workers, self.current_workers + 1)
            logger.info(f"Increasing thread pool size to {new_size}")
            return new_size
            
        return None
    
    def get_executor(self) -> concurrent.futures.ThreadPoolExecutor:
        """Get or create thread pool executor with adaptive sizing."""
        # Check if we need to adjust pool size
        new_size = self._should_adjust_pool_size()
        
        if new_size is not None and new_size != self.current_workers:
            # Shutdown existing executor
            if self.executor:
                self.executor.shutdown(wait=False)
            
            # Create new executor with adjusted size
            self.current_workers = new_size
            self.executor = concurrent.futures.ThreadPoolExecutor(
                max_workers=self.current_workers,
                thread_name_prefix="TNGD-Backup"
            )
            self._last_adjustment = time.time()
        
        # Create initial executor if needed
        elif self.executor is None:
            self.executor = concurrent.futures.ThreadPoolExecutor(
                max_workers=self.current_workers,
                thread_name_prefix="TNGD-Backup"
            )
        
        return self.executor
    
    def shutdown(self):
        """Shutdown the thread pool."""
        if self.executor:
            self.executor.shutdown(wait=True)
            self.executor = None


class ParallelTableProcessor:
    """
    Parallel table processor with selective parallelization and resource management.
    """
    
    def __init__(self, config: BackupConfig):
        self.config = config
        self.performance_monitor = PerformanceMonitor(config.config_manager)
        self.resource_monitor = ResourceMonitor(config)
        self.thread_pool = AdaptiveThreadPool(config, self.resource_monitor)
        self.processing_mode = self._determine_processing_mode()
        
        # Task management
        self.task_queue = queue.PriorityQueue()
        self.results = {}
        self.failed_tasks = []
        
        logger.info(f"Parallel processor initialized with mode: {self.processing_mode.value}")
    
    def _determine_processing_mode(self) -> ProcessingMode:
        """Determine the optimal processing mode based on configuration and resources."""
        if not self.config.parallel:
            return ProcessingMode.SEQUENTIAL
        
        # Check system resources
        usage = self.resource_monitor.get_current_usage()
        if usage['memory_percent'] > 80:
            logger.warning("High memory usage detected, falling back to sequential processing")
            return ProcessingMode.SEQUENTIAL
        
        return ProcessingMode.ADAPTIVE if self.config.adaptive_thread_pool else ProcessingMode.PARALLEL
    
    def process_tables(self, table_names: List[str], **options) -> Dict[str, Any]:
        """
        Process tables using selective parallelization.
        
        Args:
            table_names: List of table names to process
            **options: Additional processing options
            
        Returns:
            Processing results with performance metrics
        """
        start_time = time.time()
        
        try:
            # Start resource monitoring
            self.resource_monitor.start_monitoring()
            
            # Create tasks
            tasks = self._create_tasks(table_names)
            
            # Process based on mode
            if self.processing_mode == ProcessingMode.SEQUENTIAL:
                results = self._process_sequential(tasks, **options)
            else:
                results = self._process_parallel(tasks, **options)
            
            # Calculate performance metrics
            total_duration = time.time() - start_time
            results['performance_metrics'] = {
                'total_duration': total_duration,
                'processing_mode': self.processing_mode.value,
                'max_concurrent_workers': self.thread_pool.current_workers,
                'tables_per_second': len(table_names) / total_duration if total_duration > 0 else 0
            }
            
            logger.info(f"Parallel processing completed in {total_duration:.2f}s using {self.processing_mode.value} mode")
            return results
            
        finally:
            # Cleanup
            self.resource_monitor.stop_monitoring()
            self.thread_pool.shutdown()
    
    def _create_tasks(self, table_names: List[str]) -> List[ParallelTask]:
        """Create processing tasks with priority and resource estimates."""
        tasks = []
        for i, table_name in enumerate(table_names):
            # Estimate task properties (can be enhanced with historical data)
            estimated_duration = 60.0  # Default estimate
            memory_requirement = 100.0  # MB estimate
            priority = 0  # Higher priority for smaller tables
            
            task = ParallelTask(
                table_name=table_name,
                priority=priority,
                estimated_duration=estimated_duration,
                memory_requirement=memory_requirement
            )
            tasks.append(task)
        
        # Sort by priority (higher priority first)
        tasks.sort(key=lambda x: x.priority, reverse=True)
        return tasks
    
    def _process_sequential(self, tasks: List[ParallelTask], **options) -> Dict[str, Any]:
        """Process tasks sequentially."""
        logger.info(f"Processing {len(tasks)} tables sequentially")
        
        results = {
            'status': 'success',
            'summary': {
                'total_tables': len(tasks),
                'successful_backups': 0,
                'failed_backups': 0,
                'total_rows_backed_up': 0
            },
            'table_results': {}
        }
        
        for task in tasks:
            result = self._process_single_table(task, **options)
            results['table_results'][task.table_name] = result
            
            if result.status == 'success':
                results['summary']['successful_backups'] += 1
                results['summary']['total_rows_backed_up'] += result.rows_processed
            else:
                results['summary']['failed_backups'] += 1
        
        return results
    
    def _process_parallel(self, tasks: List[ParallelTask], **options) -> Dict[str, Any]:
        """Process tasks in parallel with resource management."""
        logger.info(f"Processing {len(tasks)} tables in parallel (max workers: {self.config.max_threads})")
        
        results = {
            'status': 'success',
            'summary': {
                'total_tables': len(tasks),
                'successful_backups': 0,
                'failed_backups': 0,
                'total_rows_backed_up': 0
            },
            'table_results': {}
        }
        
        # Submit tasks to thread pool
        executor = self.thread_pool.get_executor()
        future_to_task = {}
        
        for task in tasks:
            future = executor.submit(self._process_single_table, task, **options)
            future_to_task[future] = task
        
        # Collect results as they complete
        for future in concurrent.futures.as_completed(future_to_task):
            task = future_to_task[future]
            try:
                result = future.result()
                results['table_results'][task.table_name] = result
                
                if result.status == 'success':
                    results['summary']['successful_backups'] += 1
                    results['summary']['total_rows_backed_up'] += result.rows_processed
                else:
                    results['summary']['failed_backups'] += 1
                    
            except Exception as e:
                logger.error(f"Task {task.table_name} generated an exception: {str(e)}")
                results['table_results'][task.table_name] = ProcessingResult(
                    table_name=task.table_name,
                    status='error',
                    duration=0.0,
                    error=str(e)
                )
                results['summary']['failed_backups'] += 1
        
        return results
    
    def _process_single_table(self, task: ParallelTask, **options) -> ProcessingResult:
        """Process a single table and return results."""
        start_time = time.time()
        thread_id = threading.current_thread().name
        
        logger.info(f"Processing table {task.table_name} on thread {thread_id}")
        
        try:
            # Create table processor for this task
            processor = UnifiedTableProcessor(self.config, strategy='smart')
            
            # Process the table
            result = processor.backup_table(task.table_name)
            
            duration = time.time() - start_time
            
            # Extract metrics from result
            rows_processed = result.get('total_rows', 0)
            status = result.get('status', 'unknown')
            error = result.get('error')
            
            return ProcessingResult(
                table_name=task.table_name,
                status=status,
                duration=duration,
                rows_processed=rows_processed,
                error=error,
                thread_id=thread_id
            )
            
        except Exception as e:
            duration = time.time() - start_time
            logger.error(f"Error processing table {task.table_name}: {str(e)}")
            
            return ProcessingResult(
                table_name=task.table_name,
                status='error',
                duration=duration,
                error=str(e),
                thread_id=thread_id
            )
