#!/usr/bin/env python3
"""
Configuration Manager Module

This module provides a centralized way to manage configuration settings
from both config.json and environment variables. It handles loading
configuration values with proper fallbacks and default values.
"""

import os
import json
import logging
import datetime
from typing import Any, Dict, List
from dotenv import load_dotenv

# Configure logging
logger = logging.getLogger(__name__)

class ConfigManager:
    """
    Centralized configuration manager for the application.
    Handles loading from config.json and environment variables with proper fallbacks.
    """

    # Default configuration values - centralized location for all defaults
    DEFAULT_CONFIG = {
        "backup": {
            "default_days": 1,
            "default_chunk_size": 500000,
            "default_max_retries": 5,
            "default_retry_delay": 5,
            "default_timeout": 1800,
            "default_max_threads": 8,
            "default_max_concurrent_tables": 3,
            "default_batch_size": 10,
            "auto_continue_chunks": True,
            "validate_backups": True
        },
        "storage": {
            "oss_path_template": "{oss_prefix}{month_name_str}/week {week_number}/{date_str}/{table_name}_{date_str}.{ext}",
            "summary_file_template": "backup_summary_{date_str}.json",
            "temp_dir": "temp",
            "compression_algorithm": "zip",  # Options: zip, tar.gz, tar.bz2, tar.xz
            "compress_level": 6,
            "streaming_chunk_size": 8192,
            "verify_compression": True,
            "save_checksums": True,
            "auto_select_algorithm": False,  # If True, automatically select best algorithm based on data
            "enable_chunked_compression": True,  # If True, use chunked compression for very large directories
            "chunked_compression_threshold_mb": 1024,  # Size threshold in MB for using chunked compression
            "max_chunk_size_mb": 500,  # Maximum size of each chunk in MB
            "chunk_naming_pattern": "{base_name}.part{chunk_number:03d}.{ext}",  # Pattern for chunk file names
            "adaptive_compression": {
                "enabled": True,  # Enable adaptive compression strategy
                "min_chunk_size": 50000,  # Minimum chunk size (rows)
                "max_chunk_size": 2000000,  # Maximum chunk size (rows)
                "initial_chunk_size": 500000,  # Initial chunk size (rows)
                "target_memory_percent": 70,  # Target memory usage percentage
                "critical_memory_percent": 85,  # Critical memory threshold percentage
                "memory_check_interval": 5,  # Check memory every N chunks
                "cpu_check_interval": 5,  # Check CPU every N chunks
                "dynamic_level_adjustment": True,  # Dynamically adjust compression level
                "min_compress_level": 1,  # Minimum compression level
                "max_compress_level": 9,  # Maximum compression level
                "processing_time_weight": 0.6,  # Weight for processing time in adjustment
                "memory_usage_weight": 0.4  # Weight for memory usage in adjustment
            }
        },
        "logging": {
            "log_dir": "logs",
            "console_level": "INFO",
            "file_level": "DEBUG",
            "max_log_files": 30,
            "log_file_size_mb": 10
        },
        "notification": {
            "send_on_completion": True,
            "send_on_error": True,
            "include_failed_tables": True
        }
    }

    def __init__(self, config_file: str = 'config.json'):
        """
        Initialize the configuration manager.

        Args:
            config_file: Path to the configuration file (default: 'config.json')
        """
        # Load environment variables
        load_dotenv()

        # Store the config file path
        self.config_file = config_file

        # Load configuration from file
        self.config = self._load_config_file()

        # Log configuration status
        if self.config:
            logger.info(f"Configuration loaded from {config_file}")
        else:
            logger.warning(f"Could not load configuration from {config_file}, using defaults")
            self.config = self.DEFAULT_CONFIG.copy()

    def _load_config_file(self) -> Dict[str, Any]:
        """
        Load configuration from the config file.

        Returns:
            Dictionary with configuration values or empty dict if file not found
        """
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    return json.load(f)
            else:
                logger.warning(f"Configuration file not found: {self.config_file}")
                return {}
        except Exception as e:
            logger.error(f"Error loading configuration file: {str(e)}")
            return {}

    def get(self, section: str, key: str, default: Any = None, value_type: type = None) -> Any:
        """
        Get a configuration value from the specified section and key with type validation.

        Args:
            section: Configuration section (e.g., 'backup', 'storage')
            key: Configuration key within the section
            default: Default value if the key is not found
            value_type: Expected type of the value (e.g., int, str, bool)
                        If provided, will attempt to convert the value to this type

        Returns:
            Configuration value (converted to the specified type if applicable) or default if not found
        """
        # Try to get from config file
        try:
            value = self.config.get(section, {}).get(key)

            # If value not found in config, try default config
            if value is None:
                value = self.DEFAULT_CONFIG.get(section, {}).get(key)

            # If still not found, use the provided default
            if value is None:
                return default

            # If a type is specified, try to convert the value
            if value_type is not None:
                try:
                    # Special handling for boolean values
                    if value_type is bool and isinstance(value, str):
                        return value.lower() in ('true', 'yes', '1', 'y', 'on')
                    # Convert to the specified type
                    return value_type(value)
                except (ValueError, TypeError) as e:
                    logger.warning(f"Failed to convert config value '{value}' to {value_type.__name__}: {str(e)}")
                    return default

            return value

        except (KeyError, AttributeError) as e:
            logger.debug(f"Error retrieving config value {section}.{key}: {str(e)}")
            return default

    def get_env(self, key: str, default: Any = None) -> Any:
        """
        Get a value from environment variables.

        Args:
            key: Environment variable name
            default: Default value if the variable is not found

        Returns:
            Environment variable value or default if not found
        """
        return os.getenv(key, default)

    def set(self, section: str, key: str, value: Any) -> bool:
        """
        Set a configuration value in the specified section and key.

        Args:
            section: Configuration section (e.g., 'backup', 'storage')
            key: Configuration key within the section
            value: Value to set

        Returns:
            True if successful, False otherwise
        """
        try:
            # Ensure the section exists
            if section not in self.config:
                self.config[section] = {}

            # Set the value
            self.config[section][key] = value
            return True
        except Exception as e:
            logger.error(f"Error setting config value {section}.{key}: {str(e)}")
            return False

    def save(self) -> bool:
        """
        Save the current configuration to the config file.

        Returns:
            True if successful, False otherwise
        """
        try:
            with open(self.config_file, 'w') as f:
                json.dump(self.config, f, indent=4)
            logger.info(f"Configuration saved to {self.config_file}")
            return True
        except Exception as e:
            logger.error(f"Error saving configuration file: {str(e)}")
            return False

    def get_backup_settings(self) -> Dict[str, Any]:
        """
        Get all backup settings.

        Returns:
            Dictionary with backup settings
        """
        return self.config.get('backup', self.DEFAULT_CONFIG['backup'])

    def get_storage_settings(self) -> Dict[str, Any]:
        """
        Get all storage settings.

        Returns:
            Dictionary with storage settings
        """
        return self.config.get('storage', self.DEFAULT_CONFIG['storage'])

    def get_oss_credentials(self) -> Dict[str, str]:
        """
        Get OSS credentials from environment variables.

        Returns:
            Dictionary with OSS credentials
        """
        return {
            'access_key_id': self.get_env('OSS_ACCESS_KEY_ID'),
            'access_key_secret': self.get_env('OSS_ACCESS_KEY_SECRET'),
            'endpoint': self.get_env('OSS_ENDPOINT'),
            'bucket': self.get_env('OSS_BUCKET'),
            'prefix': self.get_env('OSS_PREFIX', 'devo/backups/')
        }

    def get_email_settings(self) -> Dict[str, str]:
        """
        Get email notification settings from environment variables.

        Returns:
            Dictionary with email settings
        """
        # Get email settings from environment variables
        smtp_server = self.get_env('SMTP_SERVER')
        smtp_port = self.get_env('SMTP_PORT')
        smtp_sender = self.get_env('SMTP_SENDER')
        smtp_receiver = self.get_env('SMTP_RECEIVER')
        smtp_password = self.get_env('SMTP_PASSWORD')

        # SECURITY FIX: Remove credential logging - never log password information
        # Check if SMTP is configured without exposing credentials
        if smtp_password:
            logger.info("SMTP authentication configured. Using secure credential handling.")
        else:
            logger.warning("SMTP password not configured. Email notifications may not work.")

        # Get mock mode setting (defaults to True for safety)
        mock_mode = self.get_env('SMTP_MOCK_MODE', 'true').lower() in ('true', '1', 'yes', 'on')

        # Return the settings
        return {
            'smtp_server': smtp_server,
            'smtp_port': smtp_port,
            'smtp_username': smtp_sender,  # Use sender email as username
            'smtp_password': smtp_password,
            'email_from': smtp_sender,
            'email_to': smtp_receiver,
            'mock_mode': mock_mode
        }

    def get_oss_path(self, table_name: str, date: datetime.datetime = None, algorithm: str = None) -> str:
        """
        Get the OSS path for a table backup using the configured template.

        Args:
            table_name: Table name
            date: Date for the backup (default: today)
            algorithm: Compression algorithm to use (default: from config)

        Returns:
            Formatted OSS path
        """
        # Use today if no date is provided
        if date is None:
            date = datetime.datetime.now()

        # Get the template
        template = self.get('storage', 'oss_path_template',
                           self.DEFAULT_CONFIG['storage']['oss_path_template'])

        # Get the OSS prefix
        oss_prefix = self.get_env('OSS_PREFIX', 'devo/backups/')

        # Format the date components
        date_str = date.strftime('%Y-%m-%d')
        month_name = date.strftime('%Y-%m')

        # Get month name as string (e.g., "May")
        month_name_str = date.strftime('%B')  # e.g., "May"

        # Calculate the week number (1-5)
        day_of_month = date.day
        week_number = (day_of_month - 1) // 7 + 1

        # Get the file extension based on the compression algorithm
        if algorithm is None:
            algorithm = self.get('storage', 'compression_algorithm', 'zip')

        # Map algorithm to file extension
        ext_map = {
            'zip': 'zip',
            'tar.gz': 'tar.gz',
            'tar.bz2': 'tar.bz2',
            'tar.xz': 'tar.xz'
        }
        ext = ext_map.get(algorithm, 'zip')

        # Format the path
        return template.format(
            oss_prefix=oss_prefix,
            month_name=month_name,
            month_name_str=month_name_str,
            week_number=week_number,
            date_str=date_str,
            table_name=table_name,
            ext=ext
        )

    def get_summary_file_path(self, date: datetime.datetime = None) -> str:
        """
        Get the path for the backup summary file.

        Args:
            date: Date for the summary (default: today)

        Returns:
            Formatted summary file path
        """
        # Use today if no date is provided
        if date is None:
            date = datetime.datetime.now()

        # Get the template
        template = self.get('storage', 'summary_file_template',
                           self.DEFAULT_CONFIG['storage']['summary_file_template'])

        # Format the date
        date_str = date.strftime('%Y-%m-%d')

        # Format the path
        return template.format(date_str=date_str)

    def get_tables_from_file(self, file_path: str = None) -> List[str]:
        """
        Get the list of tables from a JSON file.

        Args:
            file_path: Path to the JSON file (default: tabletest/tables.json)

        Returns:
            List of table names
        """
        # Use default path if not provided
        if file_path is None:
            file_path = 'tabletest/tables.json'

        try:
            # Import here to avoid circular imports
            from utils.input_validation import validate_file_path, validate_tables_list

            # Validate the file path
            is_valid, error = validate_file_path(file_path, must_exist=True)
            if not is_valid:
                logger.error(error)
                return []

            # Load the file
            with open(file_path, 'r') as f:
                tables = json.load(f)

            # Validate the content
            if not isinstance(tables, list):
                logger.error(f"Invalid tables file format: {file_path}. Expected a list of table names.")
                return []

            # Validate each table name
            all_valid, valid_tables, invalid_tables = validate_tables_list(tables)

            # Log any invalid table names
            if not all_valid:
                logger.warning(f"Found {len(invalid_tables)} invalid table names in {file_path}:")
                for table in invalid_tables:
                    logger.warning(f"  - Invalid table name: {table}")

            logger.info(f"Loaded {len(valid_tables)} valid tables from {file_path}")
            return valid_tables

        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON format in {file_path}: {str(e)}")
            return []
        except Exception as e:
            logger.error(f"Error loading tables file: {str(e)}")
            return []
