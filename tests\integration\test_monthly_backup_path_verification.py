#!/usr/bin/env python3
"""
Monthly Backup Path Verification Test
Verify that monthly backup functionality uses the new OSS path structure
"""

import os
import sys
from datetime import datetime, date

# Add the project root to the path (go up two levels from tests/integration/)
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

try:
    from core.config_manager import ConfigManager
    from core.backup_config import BackupConfig
    from core.unified_table_processor import UnifiedTableProcessor
    from scripts.historical_backup_processor import HistoricalBackupProcessor
    from scripts.daily_backup_scheduler import DailyBackupScheduler
except ImportError as e:
    print(f"Import error: {e}")
    print("Please ensure all dependencies are installed")
    sys.exit(1)

def test_monthly_backup_path_verification():
    """Test that monthly backup components use the new OSS path structure."""
    print("🧪 Monthly Backup Path Verification Test")
    print("=" * 70)
    print(f"Test Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    success_count = 0
    total_tests = 5
    
    # Test 1: Verify UnifiedTableProcessor uses ConfigManager.get_oss_path()
    print("1️⃣ Testing UnifiedTableProcessor path generation...")
    try:
        config = BackupConfig()
        config.specific_date = datetime(2025, 6, 19, 12, 0, 0)
        processor = UnifiedTableProcessor(config, strategy='smart')
        
        # Check if the processor has access to config_manager
        if hasattr(processor.strategy, 'config') and hasattr(processor.strategy.config, 'config_manager'):
            config_manager = processor.strategy.config.config_manager
            test_path = config_manager.get_oss_path("test_table_123456", datetime(2025, 6, 19), "tar.gz")
            expected_pattern = "Devo/June/week 3/2025-06-19/test_table_123456_2025-06-19.tar.gz"
            
            if test_path == expected_pattern:
                print("   ✅ UnifiedTableProcessor path generation: PASSED")
                success_count += 1
            else:
                print(f"   ❌ UnifiedTableProcessor path generation: FAILED")
                print(f"      Expected: {expected_pattern}")
                print(f"      Got: {test_path}")
        else:
            print("   ❌ UnifiedTableProcessor path generation: FAILED (no config_manager access)")
    except Exception as e:
        print(f"   ❌ UnifiedTableProcessor path generation: ERROR - {e}")
    
    # Test 2: Verify HistoricalBackupProcessor uses UnifiedTableProcessor
    print("\n2️⃣ Testing HistoricalBackupProcessor integration...")
    try:
        processor = HistoricalBackupProcessor()
        test_date = date(2025, 6, 19)
        config = processor.create_backup_config(test_date)
        
        # Check if config is properly created
        if config.specific_date and config.specific_date.date() == test_date:
            print("   ✅ HistoricalBackupProcessor integration: PASSED")
            success_count += 1
        else:
            print("   ❌ HistoricalBackupProcessor integration: FAILED")
    except Exception as e:
        print(f"   ❌ HistoricalBackupProcessor integration: ERROR - {e}")
    
    # Test 3: Verify DailyBackupScheduler uses UnifiedTableProcessor
    print("\n3️⃣ Testing DailyBackupScheduler integration...")
    try:
        scheduler = DailyBackupScheduler()
        
        # Check if scheduler can create processor
        config = BackupConfig()
        processor = UnifiedTableProcessor(config, strategy='smart')
        
        if processor and hasattr(processor, 'strategy'):
            print("   ✅ DailyBackupScheduler integration: PASSED")
            success_count += 1
        else:
            print("   ❌ DailyBackupScheduler integration: FAILED")
    except Exception as e:
        print(f"   ❌ DailyBackupScheduler integration: ERROR - {e}")
    
    # Test 4: Verify path consistency across components
    print("\n4️⃣ Testing path consistency across components...")
    try:
        config_manager = ConfigManager()
        test_date = datetime(2025, 6, 19, 12, 0, 0)
        table_name = "test_monthly_backup_123456"
        
        # Generate path using ConfigManager directly
        direct_path = config_manager.get_oss_path(table_name, test_date, "tar.gz")
        
        # Generate path through BackupConfig
        backup_config = BackupConfig()
        backup_config.specific_date = test_date
        backup_config.config_manager = config_manager
        
        # Both should use the same ConfigManager instance
        if backup_config.config_manager == config_manager:
            print("   ✅ Path consistency across components: PASSED")
            success_count += 1
        else:
            print("   ❌ Path consistency across components: FAILED")
    except Exception as e:
        print(f"   ❌ Path consistency across components: ERROR - {e}")
    
    # Test 5: Verify no hardcoded paths in monthly backup components
    print("\n5️⃣ Testing for hardcoded path references...")
    try:
        # Check if we can find any hardcoded "backup/daily" references
        # This is a code inspection test
        
        # Read the historical backup processor source
        with open('scripts/historical_backup_processor.py', 'r') as f:
            historical_content = f.read()
        
        # Read the daily backup scheduler source
        with open('scripts/daily_backup_scheduler.py', 'r') as f:
            daily_content = f.read()
        
        hardcoded_paths_found = []
        
        # Check for hardcoded backup/daily paths
        if 'backup/daily' in historical_content:
            hardcoded_paths_found.append('historical_backup_processor.py')
        
        if 'backup/daily' in daily_content:
            hardcoded_paths_found.append('daily_backup_scheduler.py')
        
        if not hardcoded_paths_found:
            print("   ✅ No hardcoded path references: PASSED")
            success_count += 1
        else:
            print(f"   ❌ Hardcoded path references found in: {', '.join(hardcoded_paths_found)}")
    except Exception as e:
        print(f"   ❌ Hardcoded path check: ERROR - {e}")
    
    # Summary
    print("\n" + "=" * 70)
    print("📊 Test Summary:")
    print("-" * 70)
    print(f"Tests Passed: {success_count}/{total_tests}")
    print(f"Success Rate: {(success_count/total_tests)*100:.1f}%")
    
    if success_count == total_tests:
        print("\n🎉 ALL TESTS PASSED! Monthly backup uses new OSS path structure!")
        print("\n✅ Key Findings:")
        print("   • UnifiedTableProcessor generates correct paths")
        print("   • HistoricalBackupProcessor properly integrates")
        print("   • DailyBackupScheduler uses UnifiedTableProcessor")
        print("   • Path generation is consistent across components")
        print("   • No hardcoded path references found")
        
        print("\n📂 Monthly Backup Path Flow:")
        print("   Monthly → Daily → DailyBackupScheduler → UnifiedTableProcessor → ConfigManager.get_oss_path()")
        print("   Historical → HistoricalBackupProcessor → UnifiedTableProcessor → ConfigManager.get_oss_path()")
        
        return True
    else:
        print(f"\n⚠️  {total_tests - success_count} TEST(S) FAILED!")
        print("Monthly backup may not be using the new path structure correctly.")
        return False

def show_monthly_backup_architecture():
    """Show the architecture of monthly backup components."""
    print("\n" + "=" * 70)
    print("🏗️ Monthly Backup Architecture:")
    print("-" * 70)
    
    print("📋 MONTHLY MODE:")
    print("   run_monthly_backup.bat")
    print("   ├── For each day in month:")
    print("   │   └── call run_daily_backup.bat")
    print("   │       └── DailyBackupScheduler")
    print("   │           └── UnifiedTableProcessor")
    print("   │               └── ConfigManager.get_oss_path()")
    print("   └── Result: Devo/{month}/week {n}/{date}/")
    
    print("\n📋 HISTORICAL MODE:")
    print("   run_monthly_backup.bat historical")
    print("   └── HistoricalBackupProcessor")
    print("       └── For each date in range:")
    print("           └── UnifiedTableProcessor")
    print("               └── ConfigManager.get_oss_path()")
    print("   └── Result: Devo/{month}/week {n}/{date}/")
    
    print("\n🔗 COMMON COMPONENTS:")
    print("   • UnifiedTableProcessor: Handles all table processing")
    print("   • ConfigManager: Provides OSS path generation")
    print("   • BackupConfig: Configuration management")
    print("   • All paths use the same template: Devo/{month_name_str}/week {week_number}/{date_str}/")

if __name__ == "__main__":
    print("🚀 TNGD Monthly Backup Path Verification Test Suite")
    print("=" * 80)
    
    # Run main verification test
    success = test_monthly_backup_path_verification()
    
    # Show architecture
    show_monthly_backup_architecture()
    
    print("\n" + "=" * 80)
    if success:
        print("✅ MONTHLY BACKUP PATH VERIFICATION: PASSED")
        print("Monthly backups are using the new OSS path structure correctly!")
    else:
        print("❌ MONTHLY BACKUP PATH VERIFICATION: FAILED")
        print("Monthly backups may need additional updates.")
    print("=" * 80)
