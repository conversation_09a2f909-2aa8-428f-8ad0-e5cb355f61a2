#!/usr/bin/env python3
"""
Integration Tests for TNGD Backup System Performance Optimizations

This module provides comprehensive integration tests to validate performance
improvements while ensuring data integrity and backward compatibility.

Test Categories:
- Parallel processing performance tests
- Incremental validation efficiency tests
- Asynchronous logging performance tests
- End-to-end performance comparison tests
- Resource usage optimization tests
- Backward compatibility tests

Author: TNGD Backup System
Date: 2025-06-23
"""

import unittest
import time
import tempfile
import shutil
import json
from pathlib import Path
from typing import Dict, Any, List
import threading
import concurrent.futures

# Add project root to path (go up two levels from tests/integration/)
import sys
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from core.backup_config import BackupConfig
from core.parallel_processor import ParallelTableProcessor
from core.incremental_validator import IncrementalValidator
from core.performance_optimizer import PerformanceOptimizer, PerformanceTester
from utils.async_logging import get_optimized_logger, shutdown_optimized_logging
from scripts.daily_backup_scheduler import DailyBackupScheduler


class TestPerformanceOptimizations(unittest.TestCase):
    """Integration tests for performance optimizations."""
    
    def setUp(self):
        """Set up test environment."""
        self.test_dir = Path(tempfile.mkdtemp(prefix="tngd_perf_test_"))
        self.test_tables = [
            "my.app.tngd.test_table_1",
            "my.app.tngd.test_table_2", 
            "my.app.tngd.test_table_3"
        ]
        
        # Create test configuration
        self.config = BackupConfig()
        self.config.parallel = True
        self.config.max_threads = 4
        self.config.max_concurrent_tables = 2
        self.config.use_incremental_validation = True
        
    def tearDown(self):
        """Clean up test environment."""
        if self.test_dir.exists():
            shutil.rmtree(self.test_dir)
        shutdown_optimized_logging()
    
    def test_parallel_processing_performance(self):
        """Test parallel processing performance improvements."""
        print("\n=== Testing Parallel Processing Performance ===")
        
        # Test sequential processing
        sequential_config = BackupConfig()
        sequential_config.parallel = False
        sequential_config.max_threads = 1
        
        start_time = time.time()
        sequential_processor = ParallelTableProcessor(sequential_config)
        sequential_result = sequential_processor.process_tables(self.test_tables[:2])  # Limit for testing
        sequential_duration = time.time() - start_time
        
        # Test parallel processing
        parallel_config = BackupConfig()
        parallel_config.parallel = True
        parallel_config.max_threads = 4
        
        start_time = time.time()
        parallel_processor = ParallelTableProcessor(parallel_config)
        parallel_result = parallel_processor.process_tables(self.test_tables[:2])  # Limit for testing
        parallel_duration = time.time() - start_time
        
        # Validate results
        self.assertEqual(sequential_result['status'], 'success')
        self.assertEqual(parallel_result['status'], 'success')
        
        # Performance metrics
        performance_metrics = parallel_result.get('performance_metrics', {})
        self.assertIn('total_duration', performance_metrics)
        self.assertIn('processing_mode', performance_metrics)
        self.assertEqual(performance_metrics['processing_mode'], 'adaptive')
        
        print(f"Sequential processing: {sequential_duration:.2f}s")
        print(f"Parallel processing: {parallel_duration:.2f}s")
        
        # Note: In a real test environment with actual data, parallel should be faster
        # For this test, we just verify the functionality works
        self.assertIsNotNone(performance_metrics.get('max_concurrent_workers'))
        
    def test_incremental_validation_efficiency(self):
        """Test incremental validation performance improvements."""
        print("\n=== Testing Incremental Validation Efficiency ===")
        
        validator = IncrementalValidator(self.config)
        
        # First validation (should perform full validation)
        start_time = time.time()
        first_result = validator.validate_tables(self.test_tables[:2])
        first_duration = time.time() - start_time
        
        # Second validation (should use cached results)
        start_time = time.time()
        second_result = validator.validate_tables(self.test_tables[:2])
        second_duration = time.time() - start_time
        
        # Validate results
        self.assertEqual(first_result['total_tables'], 2)
        self.assertEqual(second_result['total_tables'], 2)
        
        # Check cache efficiency
        cache_hit_rate = second_result['performance_metrics']['cache_hit_rate']
        validation_efficiency = second_result['performance_metrics']['validation_efficiency']
        
        print(f"First validation: {first_duration:.2f}s")
        print(f"Second validation: {second_duration:.2f}s")
        print(f"Cache hit rate: {cache_hit_rate:.2%}")
        print(f"Validation efficiency: {validation_efficiency:.2%}")
        
        # Second validation should be faster due to caching
        self.assertGreater(cache_hit_rate, 0, "Cache should have some hits on second validation")
        self.assertGreater(validation_efficiency, 0, "Validation efficiency should be positive")
        
    def test_async_logging_performance(self):
        """Test asynchronous logging performance."""
        print("\n=== Testing Asynchronous Logging Performance ===")
        
        # Test synchronous logging (baseline)
        import logging
        sync_logger = logging.getLogger("sync_test")
        sync_handler = logging.StreamHandler()
        sync_logger.addHandler(sync_handler)
        
        start_time = time.time()
        for i in range(1000):
            sync_logger.info(f"Synchronous log message {i}")
        sync_duration = time.time() - start_time
        
        # Test asynchronous logging
        async_config = {
            'queue_size': 10000,
            'batch_size': 100,
            'flush_interval': 0.1,
            'log_directory': str(self.test_dir)
        }
        async_logger = get_optimized_logger("async_test", async_config)
        
        start_time = time.time()
        for i in range(1000):
            async_logger.info(f"Asynchronous log message {i}")
        async_duration = time.time() - start_time
        
        # Allow time for async processing
        time.sleep(0.5)
        
        # Get performance stats
        stats = async_logger.get_performance_stats()
        
        print(f"Synchronous logging: {sync_duration:.3f}s")
        print(f"Asynchronous logging: {async_duration:.3f}s")
        print(f"Messages processed: {stats['messages_processed']}")
        print(f"Messages dropped: {stats['messages_dropped']}")
        print(f"Batch writes: {stats['batch_writes']}")
        
        # Async logging should be faster
        self.assertLess(async_duration, sync_duration * 2, "Async logging should not be significantly slower")
        self.assertGreater(stats['messages_processed'], 900, "Most messages should be processed")
        self.assertLess(stats['messages_dropped'], 100, "Few messages should be dropped")
        
    def test_performance_optimizer_integration(self):
        """Test performance optimizer integration."""
        print("\n=== Testing Performance Optimizer Integration ===")
        
        optimizer = PerformanceOptimizer(self.config)
        
        # Start monitoring
        optimizer.start_operation_monitoring("integration_test")
        optimizer.checkpoint("test_start")
        
        # Simulate some work
        time.sleep(0.1)
        optimizer.checkpoint("work_phase_1")
        
        time.sleep(0.1)
        optimizer.checkpoint("work_phase_2")
        
        # End monitoring
        performance_data = optimizer.end_operation_monitoring(
            "integration_test",
            tables_processed=3,
            rows_processed=1000
        )
        
        # Validate performance data
        self.assertIn('profile', performance_data)
        self.assertIn('metrics', performance_data)
        self.assertIn('recommendations', performance_data)
        
        profile = performance_data['profile']
        self.assertIn('checkpoints', profile)
        self.assertGreater(len(profile['checkpoints']), 2)
        
        metrics = performance_data['metrics']
        self.assertEqual(metrics['operation_type'], 'integration_test')
        self.assertEqual(metrics['tables_processed'], 3)
        self.assertEqual(metrics['rows_processed'], 1000)
        
        print(f"Operation duration: {profile['total_duration']:.3f}s")
        print(f"Checkpoints: {len(profile['checkpoints'])}")
        print(f"Recommendations: {len(performance_data['recommendations'])}")
        
        # Generate performance report
        report = optimizer.get_performance_report()
        self.assertEqual(report['status'], 'success')
        self.assertIn('summary', report)
        
    def test_end_to_end_performance_comparison(self):
        """Test end-to-end performance comparison."""
        print("\n=== Testing End-to-End Performance Comparison ===")
        
        # Test scenarios
        test_scenarios = [
            {
                'name': 'baseline_sequential',
                'config': {
                    'parallel': False,
                    'max_threads': 1,
                    'use_incremental_validation': False
                },
                'max_tables': 2
            },
            {
                'name': 'optimized_parallel',
                'config': {
                    'parallel': True,
                    'max_threads': 4,
                    'use_incremental_validation': True,
                    'max_concurrent_tables': 2
                },
                'max_tables': 2
            }
        ]
        
        # Run performance tests
        tester = PerformanceTester(self.config)
        results = tester.run_performance_test(self.test_tables, test_scenarios)
        
        # Validate results
        self.assertEqual(len(results['scenarios']), 2)
        self.assertIn('summary', results)
        
        summary = results['summary']
        if summary['status'] == 'success':
            print(f"Best scenario: {summary['best_scenario']}")
            print(f"Best duration: {summary['best_duration']:.3f}s")
            print(f"Average duration: {summary['average_duration']:.3f}s")
            
            improvement = summary.get('performance_improvement', {})
            if improvement.get('status') == 'calculated':
                print(f"Performance improvement: {improvement['improvement_percent']:.1f}%")
        
        # At minimum, tests should complete successfully
        successful_scenarios = [s for s in results['scenarios'] if s.get('status') == 'success']
        self.assertGreater(len(successful_scenarios), 0, "At least one scenario should succeed")
        
    def test_resource_usage_optimization(self):
        """Test resource usage optimization."""
        print("\n=== Testing Resource Usage Optimization ===")
        
        from core.parallel_processor import ResourceMonitor
        
        monitor = ResourceMonitor(self.config)
        monitor.start_monitoring()
        
        # Get initial resource usage
        initial_usage = monitor.get_current_usage()
        
        # Simulate some load
        def cpu_intensive_task():
            for _ in range(100000):
                _ = sum(range(100))
        
        # Run tasks
        with concurrent.futures.ThreadPoolExecutor(max_workers=2) as executor:
            futures = [executor.submit(cpu_intensive_task) for _ in range(4)]
            concurrent.futures.wait(futures)
        
        # Check if resource monitoring detects high usage
        time.sleep(1)  # Allow monitoring to detect changes
        should_reduce = monitor.should_reduce_concurrency()
        
        monitor.stop_monitoring()
        
        print(f"Initial CPU usage: {initial_usage['cpu_percent']:.1f}%")
        print(f"Initial memory usage: {initial_usage['memory_percent']:.1f}%")
        print(f"Should reduce concurrency: {should_reduce}")
        
        # Validate monitoring functionality
        self.assertIsInstance(initial_usage['cpu_percent'], (int, float))
        self.assertIsInstance(initial_usage['memory_percent'], (int, float))
        self.assertIsInstance(should_reduce, bool)
        
    def test_backward_compatibility(self):
        """Test backward compatibility with existing functionality."""
        print("\n=== Testing Backward Compatibility ===")
        
        # Test that old configuration still works
        old_style_config = BackupConfig()
        old_style_config.parallel = False
        old_style_config.max_threads = 1
        
        # Should work without new optimization features
        from core.unified_table_processor import UnifiedTableProcessor
        processor = UnifiedTableProcessor(old_style_config, strategy='smart')
        
        # This should not fail even with limited test tables
        try:
            result = processor.process_tables(self.test_tables[:1], skip_empty=True)
            compatibility_test_passed = True
        except Exception as e:
            print(f"Backward compatibility test failed: {str(e)}")
            compatibility_test_passed = False
        
        self.assertTrue(compatibility_test_passed, "Backward compatibility should be maintained")
        
        # Test that new features can be disabled
        disabled_config = BackupConfig()
        disabled_config.parallel = False
        disabled_config.use_incremental_validation = False
        
        # Should work with optimizations disabled
        try:
            processor_disabled = UnifiedTableProcessor(disabled_config, strategy='smart')
            result_disabled = processor_disabled.process_tables(self.test_tables[:1], skip_empty=True)
            disabled_features_test_passed = True
        except Exception as e:
            print(f"Disabled features test failed: {str(e)}")
            disabled_features_test_passed = False
        
        self.assertTrue(disabled_features_test_passed, "System should work with optimizations disabled")
        
        print("Backward compatibility tests passed")


def run_performance_tests():
    """Run all performance optimization tests."""
    print("=" * 60)
    print("TNGD Backup System - Performance Optimization Tests")
    print("=" * 60)
    
    # Create test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(TestPerformanceOptimizations)
    
    # Run tests with detailed output
    runner = unittest.TextTestRunner(verbosity=2, stream=sys.stdout)
    result = runner.run(suite)
    
    # Print summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print("\nFAILURES:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\nERRORS:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    success_rate = ((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun) * 100
    print(f"\nSuccess rate: {success_rate:.1f}%")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_performance_tests()
    sys.exit(0 if success else 1)
