#!/usr/bin/env python3
"""
Logging Adapter for TNGD Backup System

This module provides backward compatibility adapters to gradually migrate
from the old logging system to the new enhanced logging system.

It allows existing code to continue working while providing enhanced features
when the new logging context is available.
"""

import logging
from typing import Optional, Dict, Any, Union
from contextlib import contextmanager

from utils.enhanced_logging import (
    OperationType, get_logger, configure_logging
)


class BackwardCompatibilityLogger:
    """
    Adapter that provides backward compatibility with the old minimal_logging interface
    while using the new enhanced logging system underneath.
    """

    def __init__(self, name: str = "tngd_backup", config: Optional[Dict[str, Any]] = None):
        self.enhanced_logger = get_logger(name, config)
        self._fallback_logger = logging.getLogger(name)

    def info(self, message: str) -> None:
        """Log info message (backward compatibility)."""
        try:
            self.enhanced_logger.info(message)
        except Exception:
            # Fallback to standard logging if enhanced logging fails
            self._fallback_logger.info(message)

    def error(self, message: str) -> None:
        """Log error message (backward compatibility)."""
        try:
            self.enhanced_logger.error(message)
        except Exception:
            self._fallback_logger.error(message)

    def warning(self, message: str) -> None:
        """Log warning message (backward compatibility)."""
        try:
            self.enhanced_logger.warning(message)
        except Exception:
            self._fallback_logger.warning(message)

    def debug(self, message: str) -> None:
        """Log debug message (backward compatibility)."""
        try:
            self.enhanced_logger.debug(message)
        except Exception:
            self._fallback_logger.debug(message)

    def log_operation(self, operation: str, status: str, details: Optional[str] = None,
                      component: Optional[str] = None, phase: Optional[str] = None) -> None:
        """
        Log an operation with its status (enhanced backward compatibility).

        This method provides the same interface as the old minimal_logging but uses
        the enhanced logging system with proper context and formatting.
        """
        try:
            # Map old status values to new phases
            phase_mapping = {
                "STARTED": "START",
                "SUCCESS": "COMPLETE",
                "FAILED": "FAILED",
                "ERROR": "FAILED",
                "WARNING": "WARNING",
                "FINISHED": "COMPLETE"
            }

            mapped_phase = phase_mapping.get(status.upper(), phase or status)

            # Determine log level based on status
            if status.upper() in ["FAILED", "ERROR"]:
                self.enhanced_logger.error(f"{operation}: {details or status}", component, mapped_phase)
            elif status.upper() == "WARNING":
                self.enhanced_logger.warning(f"{operation}: {details or status}", component, mapped_phase)
            else:
                self.enhanced_logger.info(f"{operation}: {details or status}", component, mapped_phase)

        except Exception:
            # Fallback to simple logging
            message = f"{operation}: {status}"
            if details:
                message += f" - {details}"
            self._fallback_logger.info(message)

    def log_structured(self, level: str, component: str, phase: str, message: str,
                       metadata: Optional[Dict[str, Any]] = None) -> None:
        """Log a structured message (enhanced backward compatibility)."""
        try:
            # Add metadata to the enhanced logger context if available
            context = self.enhanced_logger._get_current_context()
            if context and metadata:
                context.metadata.update(metadata)

            # Log with appropriate level
            level_upper = level.upper()
            if level_upper == "ERROR":
                self.enhanced_logger.error(message, component, phase)
            elif level_upper == "WARNING":
                self.enhanced_logger.warning(message, component, phase)
            elif level_upper == "DEBUG":
                self.enhanced_logger.debug(message, component, phase)
            else:
                self.enhanced_logger.info(message, component, phase)

        except Exception:
            # Fallback to simple logging
            self._fallback_logger.log(getattr(logging, level.upper(), logging.INFO),
                                    f"[{component}] {message}")

    def log_backup_summary(self, successful: int, failed: int, duration: float) -> None:
        """Log a backup summary (backward compatibility)."""
        try:
            total = successful + failed
            success_rate = (successful / total * 100) if total > 0 else 0

            summary_msg = f"Backup Summary: {success_rate:.1f}% success rate ({successful}/{total}), duration: {self._format_duration(duration)}"

            if failed > 0:
                self.enhanced_logger.warning(summary_msg, "backup_summary", "COMPLETE")
            else:
                self.enhanced_logger.info(summary_msg, "backup_summary", "COMPLETE")

        except Exception:
            # Fallback to simple logging
            self._fallback_logger.info(f"Backup completed: {successful} successful, {failed} failed, {duration:.1f}s")

    @staticmethod
    def _format_duration(seconds: float) -> str:
        """Format duration in a human-readable format."""
        if seconds < 60:
            return f"{seconds:.1f}s"
        elif seconds < 3600:
            minutes = seconds // 60
            remaining_seconds = seconds % 60
            return f"{int(minutes)}m {int(remaining_seconds)}s"
        else:
            hours = seconds // 3600
            minutes = (seconds % 3600) // 60
            return f"{int(hours)}h {int(minutes)}m"

    @contextmanager
    def operation_context(self, operation_type: Union[str, OperationType], component: str,
                         table_name: Optional[str] = None, metadata: Optional[Dict[str, Any]] = None):
        """
        Enhanced operation context manager.

        Args:
            operation_type: Type of operation (string or OperationType enum)
            component: Component performing the operation
            table_name: Optional table name
            metadata: Optional metadata
        """
        # Convert string operation types to enum
        if isinstance(operation_type, str):
            type_mapping = {
                "daily_backup": OperationType.DAILY_BACKUP,
                "monthly_backup": OperationType.MONTHLY_BACKUP,
                "historical_backup": OperationType.HISTORICAL_BACKUP,
                "single_table": OperationType.SINGLE_TABLE,
                "system_maintenance": OperationType.SYSTEM_MAINTENANCE,
                "validation": OperationType.VALIDATION
            }
            operation_type = type_mapping.get(operation_type.lower(), OperationType.SYSTEM_MAINTENANCE)

        with self.enhanced_logger.operation_context(operation_type, component, table_name, metadata) as context:
            yield context


# Global adapter instance for backward compatibility
_global_adapter = None


def get_adapter(name: str = "tngd_backup", config: Optional[Dict[str, Any]] = None) -> BackwardCompatibilityLogger:
    """Get or create a backward compatibility logger adapter."""
    global _global_adapter
    if _global_adapter is None:
        _global_adapter = BackwardCompatibilityLogger(name, config)
    return _global_adapter


def initialize_enhanced_logging(config: Optional[Dict[str, Any]] = None):
    """Initialize the enhanced logging system with configuration."""
    if config:
        configure_logging(config)

    # Create global adapter
    global _global_adapter
    _global_adapter = BackwardCompatibilityLogger("tngd_backup", config)


# Create a default adapter instance that mimics the old minimal_logging interface
logger = get_adapter()