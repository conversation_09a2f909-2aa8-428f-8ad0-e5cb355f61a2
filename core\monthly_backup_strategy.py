#!/usr/bin/env python3
"""
Monthly Backup Strategy Module

This module provides comprehensive monthly backup strategies designed to ensure
100% success rate for backing up all tables from the ddevo database to OSS.

Key Features:
- Multiple backup strategies optimized for different scenarios
- Robust error handling with exponential backoff
- Data consistency validation
- Storage optimization techniques
- Recovery time optimization
- Network stability handling
- Progress checkpointing and resumption

Strategies:
1. Day-by-Day Sequential Strategy (Recommended)
2. Week-by-Week Chunked Strategy
3. Hybrid Adaptive Strategy
4. Emergency Fallback Strategy

Each strategy is designed to handle large data volumes, network instability,
and storage constraints while maintaining data integrity.
"""

import datetime
import json
import sys
from typing import Dict, Any, List, Optional, Tuple
from enum import Enum
from dataclasses import dataclass
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Import project modules
from core.backup_config import BackupConfig
from core.config_manager import ConfigManager
from core.unified_table_processor import UnifiedTableProcessor
from utils.minimal_logging import logger

class BackupStrategy(Enum):
    """Enumeration of available backup strategies."""
    DAY_BY_DAY = "day_by_day"
    WEEK_BY_WEEK = "week_by_week"
    HYBRID_ADAPTIVE = "hybrid_adaptive"
    EMERGENCY_FALLBACK = "emergency_fallback"

@dataclass
class MonthlyBackupConfig:
    """Configuration for monthly backup operations."""
    month: int
    year: int
    strategy: BackupStrategy
    max_retries_per_chunk: int = 5
    retry_delay_minutes: int = 30
    checkpoint_interval_hours: int = 2
    validate_chunks: bool = True
    parallel_chunks: bool = False
    storage_optimization: bool = True
    network_timeout_minutes: int = 60
    max_chunk_size_days: int = 7

class MonthlyBackupStrategy:
    """
    Monthly backup strategy manager that implements various backup approaches
    optimized for reliability, efficiency, and data integrity.
    """

    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """
        Initialize the monthly backup strategy manager.

        Args:
            config_manager: Configuration manager instance
        """
        self.config_manager = config_manager or ConfigManager()
        self.checkpoint_file = None
        self.current_progress = {}

    def recommend_strategy(self, month: int, year: int, table_count: int) -> BackupStrategy:
        """
        Recommend the best backup strategy based on various factors.

        Args:
            month: Month to backup (1-12)
            year: Year to backup
            table_count: Number of tables to backup

        Returns:
            Recommended backup strategy
        """
        # Calculate estimated data volume
        days_in_month = self._get_days_in_month(month, year)

        # Strategy recommendation logic
        if table_count <= 20 and days_in_month <= 31:
            # Small to medium dataset - use day-by-day for maximum reliability
            return BackupStrategy.DAY_BY_DAY
        elif table_count <= 50:
            # Medium dataset - use week-by-week for efficiency
            return BackupStrategy.WEEK_BY_WEEK
        else:
            # Large dataset - use hybrid adaptive approach
            return BackupStrategy.HYBRID_ADAPTIVE

    def execute_monthly_backup(self, backup_config: MonthlyBackupConfig,
                             table_names: List[str]) -> Dict[str, Any]:
        """
        Execute monthly backup using the specified strategy.

        Args:
            backup_config: Monthly backup configuration
            table_names: List of tables to backup

        Returns:
            Backup execution results
        """
        logger.info("=" * 50)
        logger.info(f"MONTHLY BACKUP - {backup_config.strategy.value.upper()}")
        logger.info("=" * 50)
        logger.info(f"Starting monthly backup for {backup_config.month}/{backup_config.year}")
        logger.info(f"Strategy: {backup_config.strategy.value}")
        logger.info(f"Tables to backup: {len(table_names)}")

        # Initialize checkpoint system
        self._initialize_checkpoint_system(backup_config, table_names)

        try:
            if backup_config.strategy == BackupStrategy.DAY_BY_DAY:
                return self._execute_day_by_day_strategy(backup_config, table_names)
            elif backup_config.strategy == BackupStrategy.WEEK_BY_WEEK:
                return self._execute_week_by_week_strategy(backup_config, table_names)
            elif backup_config.strategy == BackupStrategy.HYBRID_ADAPTIVE:
                return self._execute_hybrid_adaptive_strategy(backup_config, table_names)
            elif backup_config.strategy == BackupStrategy.EMERGENCY_FALLBACK:
                return self._execute_emergency_fallback_strategy(backup_config, table_names)
            else:
                raise ValueError(f"Unknown backup strategy: {backup_config.strategy}")

        except Exception as e:
            logger.error(f"Monthly backup failed: {str(e)}")
            return {
                'status': 'error',
                'error': str(e),
                'strategy': backup_config.strategy.value,
                'progress': self.current_progress
            }

    def _execute_day_by_day_strategy(self, backup_config: MonthlyBackupConfig,
                                   table_names: List[str]) -> Dict[str, Any]:
        """
        Execute day-by-day backup strategy (RECOMMENDED).

        This strategy processes each day of the month sequentially for all tables.
        It provides maximum reliability and is easiest to resume if interrupted.

        Advantages:
        - Highest success rate
        - Easy to resume from interruptions
        - Minimal resource usage
        - Clear progress tracking
        - Excellent for network instability

        Args:
            backup_config: Monthly backup configuration
            table_names: List of tables to backup

        Returns:
            Strategy execution results
        """
        logger.info("Executing Day-by-Day Sequential Strategy")

        days_in_month = self._get_days_in_month(backup_config.month, backup_config.year)
        total_chunks = days_in_month * len(table_names)
        completed_chunks = 0
        failed_chunks = []

        results = {
            'status': 'success',
            'strategy': 'day_by_day',
            'total_chunks': total_chunks,
            'completed_chunks': 0,
            'failed_chunks': [],
            'table_results': {},
            'daily_progress': {}
        }

        # Process each day sequentially
        for day in range(1, days_in_month + 1):
            day_date = datetime.date(backup_config.year, backup_config.month, day)
            logger.info(f"Processing day {day}/{days_in_month}: {day_date}")

            day_results = {
                'date': day_date.isoformat(),
                'tables_processed': 0,
                'tables_failed': 0,
                'total_rows': 0
            }

            # Process each table for this day
            for table_name in table_names:
                try:
                    # Create backup configuration for this specific day
                    day_config = self._create_day_backup_config(backup_config, day_date)

                    # Execute backup for this table and day
                    table_result = self._backup_table_for_date(
                        table_name, day_date, day_config
                    )

                    if table_result['status'] == 'success':
                        day_results['tables_processed'] += 1
                        day_results['total_rows'] += table_result.get('rows', 0)
                        completed_chunks += 1
                    else:
                        day_results['tables_failed'] += 1
                        failed_chunks.append({
                            'table': table_name,
                            'date': day_date.isoformat(),
                            'error': table_result.get('error', 'Unknown error')
                        })

                    # Update progress
                    self._update_progress(backup_config, day, table_name, table_result)

                except Exception as e:
                    logger.error(f"Failed to backup {table_name} for {day_date}: {str(e)}")
                    day_results['tables_failed'] += 1
                    failed_chunks.append({
                        'table': table_name,
                        'date': day_date.isoformat(),
                        'error': str(e)
                    })

            results['daily_progress'][day_date.isoformat()] = day_results
            results['completed_chunks'] = completed_chunks
            results['failed_chunks'] = failed_chunks

            # Send progress update if configured
            if self._should_send_progress_update(backup_config):
                self._send_progress_notification(backup_config, results)

            # Save checkpoint
            self._save_checkpoint(backup_config, results)

            logger.info(f"Day {day} completed: {day_results['tables_processed']} success, "
                       f"{day_results['tables_failed']} failed")

        # Final status determination
        if failed_chunks:
            results['status'] = 'partial' if completed_chunks > 0 else 'failed'

        logger.info(f"Day-by-day strategy completed: {completed_chunks}/{total_chunks} chunks successful")
        return results

    def _execute_week_by_week_strategy(self, backup_config: MonthlyBackupConfig,
                                     table_names: List[str]) -> Dict[str, Any]:
        """
        Execute week-by-week backup strategy.

        This strategy processes data in weekly chunks, providing a balance
        between efficiency and reliability.

        Args:
            backup_config: Monthly backup configuration
            table_names: List of tables to backup

        Returns:
            Strategy execution results
        """
        logger.info("Executing Week-by-Week Chunked Strategy")

        # Generate weekly date ranges
        week_ranges = self._generate_weekly_ranges(backup_config.month, backup_config.year)
        total_chunks = len(week_ranges) * len(table_names)

        results = {
            'status': 'success',
            'strategy': 'week_by_week',
            'total_chunks': total_chunks,
            'completed_chunks': 0,
            'failed_chunks': [],
            'weekly_progress': {}
        }

        # Process each week
        for week_num, (start_date, end_date) in enumerate(week_ranges, 1):
            logger.info(f"Processing week {week_num}/{len(week_ranges)}: {start_date} to {end_date}")

            week_results = self._process_week_chunk(
                backup_config, table_names, start_date, end_date
            )

            results['weekly_progress'][f"week_{week_num}"] = week_results
            results['completed_chunks'] += week_results.get('successful_tables', 0)
            results['failed_chunks'].extend(week_results.get('failed_tables', []))

        return results

    def _execute_hybrid_adaptive_strategy(self, backup_config: MonthlyBackupConfig,
                                        table_names: List[str]) -> Dict[str, Any]:
        """
        Execute hybrid adaptive backup strategy.

        This strategy adapts chunk size based on table characteristics and
        system performance, optimizing for both speed and reliability.

        Args:
            backup_config: Monthly backup configuration
            table_names: List of tables to backup

        Returns:
            Strategy execution results
        """
        logger.info("Executing Hybrid Adaptive Strategy")

        # Analyze tables and determine optimal chunking
        table_analysis = self._analyze_tables_for_chunking(table_names)

        results = {
            'status': 'success',
            'strategy': 'hybrid_adaptive',
            'table_analysis': table_analysis,
            'adaptive_chunks': [],
            'completed_chunks': 0,
            'failed_chunks': []
        }

        # Process tables with adaptive chunking
        for table_name, analysis in table_analysis.items():
            chunk_strategy = analysis['recommended_strategy']

            if chunk_strategy == 'daily':
                table_result = self._process_table_daily_chunks(
                    backup_config, table_name
                )
            elif chunk_strategy == 'weekly':
                table_result = self._process_table_weekly_chunks(
                    backup_config, table_name
                )
            else:  # monthly
                table_result = self._process_table_monthly_chunk(
                    backup_config, table_name
                )

            results['adaptive_chunks'].append({
                'table': table_name,
                'strategy': chunk_strategy,
                'result': table_result
            })

        return results

    def _execute_emergency_fallback_strategy(self, backup_config: MonthlyBackupConfig,
                                           table_names: List[str]) -> Dict[str, Any]:
        """
        Execute emergency fallback backup strategy.

        This strategy uses the most conservative approach with maximum retries
        and minimal chunk sizes for maximum reliability.

        Args:
            backup_config: Monthly backup configuration
            table_names: List of tables to backup

        Returns:
            Strategy execution results
        """
        logger.info("Executing Emergency Fallback Strategy")

        # Use most conservative settings
        emergency_config = MonthlyBackupConfig(
            month=backup_config.month,
            year=backup_config.year,
            strategy=BackupStrategy.EMERGENCY_FALLBACK,
            max_retries_per_chunk=10,
            retry_delay_minutes=60,
            checkpoint_interval_hours=1,
            validate_chunks=True,
            parallel_chunks=False,
            max_chunk_size_days=1  # Daily chunks only
        )

        # Execute day-by-day with maximum safety
        return self._execute_day_by_day_strategy(emergency_config, table_names)

    def _get_days_in_month(self, month: int, year: int) -> int:
        """Get the number of days in a specific month."""
        if month == 12:
            next_month = datetime.date(year + 1, 1, 1)
        else:
            next_month = datetime.date(year, month + 1, 1)

        last_day = next_month - datetime.timedelta(days=1)
        return last_day.day

    def _create_day_backup_config(self, backup_config: MonthlyBackupConfig,
                                 target_date: datetime.date) -> BackupConfig:
        """Create backup configuration for a specific day."""
        config = BackupConfig()
        config.specific_date = target_date
        config.max_retries = backup_config.max_retries_per_chunk
        config.retry_delay = backup_config.retry_delay_minutes * 60
        config.timeout = backup_config.network_timeout_minutes * 60
        config.validate = backup_config.validate_chunks
        return config

    def _backup_table_for_date(self, table_name: str, target_date: datetime.date,
                              config: BackupConfig) -> Dict[str, Any]:
        """
        Backup a single table for a specific date using UnifiedTableProcessor.

        Args:
            table_name: Name of the table to backup
            target_date: Date to backup
            config: Backup configuration

        Returns:
            Backup result with actual data from backup operation
        """
        start_time = datetime.datetime.now()

        try:
            logger.info(f"Backing up {table_name} for {target_date}")

            # Ensure config has the specific date set
            config.specific_date = target_date

            # Use UnifiedTableProcessor to perform actual backup
            processor = UnifiedTableProcessor(config, strategy='smart')
            result = processor.process_tables([table_name])

            # Calculate duration
            end_time = datetime.datetime.now()
            duration_seconds = (end_time - start_time).total_seconds()

            # Transform result to match expected format
            # process_tables returns a dict with 'status', 'summary', and 'table_results'
            table_result = result.get('table_results', {}).get(table_name, {})
            summary = result.get('summary', {})

            if result.get('status') == 'success' and table_result.get('status') == 'success':
                rows_backed_up = table_result.get('rows_backed_up', summary.get('total_rows_backed_up', 0))
                logger.info(f"Successfully backed up {rows_backed_up} rows from {table_name} for {target_date}")

                return {
                    'status': 'success',
                    'table': table_name,
                    'date': target_date.isoformat(),
                    'rows': rows_backed_up,
                    'size_bytes': table_result.get('size_bytes', summary.get('total_size_bytes', 0)),
                    'duration_seconds': duration_seconds,
                    'backup_file': table_result.get('backup_file', ''),
                    'oss_path': table_result.get('oss_path', ''),
                    'chunks_processed': table_result.get('chunks_processed', 1),
                    'processing_time': table_result.get('processing_time', duration_seconds)
                }
            elif table_result.get('status') == 'skipped':
                # Handle skipped tables (e.g., no data for date range)
                skip_reason = table_result.get('message', table_result.get('reason', 'Table skipped'))
                logger.info(f"Skipped {table_name} for {target_date}: {skip_reason}")

                return {
                    'status': 'skipped',
                    'table': table_name,
                    'date': target_date.isoformat(),
                    'reason': skip_reason,
                    'rows': 0,
                    'duration_seconds': duration_seconds
                }
            else:
                # Handle error case with enhanced context
                error_msg = table_result.get('error') or result.get('error') or 'Unknown backup error'

                # Add more context to error messages
                if 'timeout' in error_msg.lower():
                    enhanced_error = f"Backup timeout for {table_name} on {target_date} after {duration_seconds:.1f}s: {error_msg}"
                elif 'connection' in error_msg.lower():
                    enhanced_error = f"Connection error backing up {table_name} on {target_date}: {error_msg}"
                elif 'permission' in error_msg.lower() or 'access' in error_msg.lower():
                    enhanced_error = f"Access error for {table_name} on {target_date}: {error_msg}"
                else:
                    enhanced_error = f"Failed to backup {table_name} on {target_date}: {error_msg}"

                logger.error(enhanced_error)

                return {
                    'status': 'error',
                    'table': table_name,
                    'date': target_date.isoformat(),
                    'error': enhanced_error,
                    'original_error': error_msg,
                    'duration_seconds': duration_seconds,
                    'context': {
                        'table_result_status': table_result.get('status'),
                        'overall_result_status': result.get('status'),
                        'summary': summary
                    }
                }

        except Exception as e:
            end_time = datetime.datetime.now()
            duration_seconds = (end_time - start_time).total_seconds()

            # Enhanced error context for troubleshooting
            error_type = type(e).__name__
            error_context = {
                'table': table_name,
                'date': target_date.isoformat(),
                'duration_seconds': duration_seconds,
                'error_type': error_type,
                'config_details': {
                    'specific_date': config.specific_date.isoformat() if config.specific_date else None,
                    'days': getattr(config, 'days', None),
                    'validate': getattr(config, 'validate', None)
                }
            }

            enhanced_error_msg = (
                f"Exception during backup of {table_name} for {target_date} "
                f"(duration: {duration_seconds:.1f}s, type: {error_type}): {str(e)}"
            )

            logger.error(enhanced_error_msg)
            logger.debug(f"Error context: {error_context}")

            return {
                'status': 'error',
                'table': table_name,
                'date': target_date.isoformat(),
                'error': enhanced_error_msg,
                'original_error': str(e),
                'error_type': error_type,
                'duration_seconds': duration_seconds,
                'context': error_context
            }

    def _initialize_checkpoint_system(self, backup_config: MonthlyBackupConfig,
                                    table_names: List[str]):
        """Initialize the checkpoint system for progress tracking."""
        checkpoint_dir = Path("logs/checkpoints")
        checkpoint_dir.mkdir(parents=True, exist_ok=True)

        self.checkpoint_file = checkpoint_dir / f"monthly_backup_{backup_config.year}_{backup_config.month:02d}.json"

        # Initialize progress tracking
        self.current_progress = {
            'month': backup_config.month,
            'year': backup_config.year,
            'strategy': backup_config.strategy.value,
            'total_tables': len(table_names),
            'start_time': datetime.datetime.now().isoformat(),
            'last_update': datetime.datetime.now().isoformat(),
            'completed_days': [],
            'failed_chunks': []
        }

    def _update_progress(self, backup_config: MonthlyBackupConfig, day: int,
                        table_name: str, result: Dict[str, Any]):
        """
        Update comprehensive progress tracking with detailed metrics and estimates.

        Args:
            backup_config: Monthly backup configuration
            day: Current day being processed
            table_name: Name of the table being processed
            result: Result from the backup operation
        """
        current_time = datetime.datetime.now()
        self.current_progress['last_update'] = current_time.isoformat()

        # Update basic progress counters
        if result.get('status') == 'success':
            if day not in self.current_progress.get('completed_days', []):
                self.current_progress.setdefault('completed_days', []).append(day)

            # Update success metrics
            self.current_progress.setdefault('successful_tables', 0)
            self.current_progress['successful_tables'] += 1
            self.current_progress.setdefault('total_rows_processed', 0)
            self.current_progress['total_rows_processed'] += result.get('rows', 0)
            self.current_progress.setdefault('total_size_bytes', 0)
            self.current_progress['total_size_bytes'] += result.get('size_bytes', 0)

        else:
            # Track failed operations
            self.current_progress.setdefault('failed_chunks', []).append({
                'day': day,
                'table': table_name,
                'error': result.get('error', 'Unknown error'),
                'timestamp': current_time.isoformat()
            })
            self.current_progress.setdefault('failed_tables', 0)
            self.current_progress['failed_tables'] += 1

        # Calculate completion percentage
        total_days = self._get_days_in_month(backup_config.month, backup_config.year)
        total_expected_operations = total_days * self.current_progress.get('total_tables', 1)
        completed_operations = (
            self.current_progress.get('successful_tables', 0) +
            self.current_progress.get('failed_tables', 0)
        )
        completion_percent = (completed_operations / max(1, total_expected_operations)) * 100
        self.current_progress['completion_percent'] = round(completion_percent, 2)

        # Calculate time estimates
        start_time = datetime.datetime.fromisoformat(self.current_progress['start_time'])
        elapsed_time = (current_time - start_time).total_seconds()
        self.current_progress['elapsed_time_seconds'] = elapsed_time
        self.current_progress['elapsed_time_formatted'] = str(datetime.timedelta(seconds=int(elapsed_time)))

        # Estimate remaining time based on current progress
        if completion_percent > 0:
            estimated_total_time = elapsed_time / (completion_percent / 100)
            remaining_time = max(0, estimated_total_time - elapsed_time)
            self.current_progress['estimated_remaining_seconds'] = remaining_time
            self.current_progress['estimated_remaining_formatted'] = str(datetime.timedelta(seconds=int(remaining_time)))
            self.current_progress['estimated_completion_time'] = (
                current_time + datetime.timedelta(seconds=remaining_time)
            ).isoformat()

        # Update current operation details
        self.current_progress['current_day'] = day
        self.current_progress['current_table'] = table_name
        self.current_progress['current_operation_status'] = result.get('status', 'unknown')

        # Calculate success rate
        total_operations = completed_operations
        successful_operations = self.current_progress.get('successful_tables', 0)
        if total_operations > 0:
            success_rate = (successful_operations / total_operations) * 100
            self.current_progress['success_rate_percent'] = round(success_rate, 2)

        # Log progress update with structured logging
        logger.log_structured(
            level="INFO",
            component="MonthlyBackupStrategy",
            phase="PROGRESS",
            message=f"Progress update: Day {day}, Table {table_name}, "
                   f"Status: {result.get('status')}, "
                   f"Completion: {completion_percent:.1f}%, "
                   f"Success Rate: {self.current_progress.get('success_rate_percent', 0):.1f}%"
        )

        # Log detailed progress every 10% completion or on errors
        if (completion_percent % 10 < 1 and completion_percent > 0) or result.get('status') == 'error':
            logger.info(f"Monthly backup progress: {completion_percent:.1f}% complete, "
                       f"Elapsed: {self.current_progress.get('elapsed_time_formatted', 'unknown')}, "
                       f"ETA: {self.current_progress.get('estimated_remaining_formatted', 'unknown')}")

            if self.current_progress.get('failed_tables', 0) > 0:
                logger.warning(f"Failed operations: {self.current_progress['failed_tables']}, "
                             f"Success rate: {self.current_progress.get('success_rate_percent', 0):.1f}%")

    def _save_checkpoint(self, backup_config: MonthlyBackupConfig, results: Dict[str, Any]):
        """
        Save comprehensive checkpoint data to file for recovery purposes.

        Args:
            backup_config: Monthly backup configuration
            results: Current backup results
        """
        if not self.checkpoint_file:
            logger.warning("No checkpoint file configured, skipping checkpoint save")
            return

        try:
            # Prepare comprehensive checkpoint data
            checkpoint_data = {
                'metadata': {
                    'checkpoint_version': '1.0',
                    'created_at': datetime.datetime.now().isoformat(),
                    'backup_config': {
                        'month': backup_config.month,
                        'year': backup_config.year,
                        'strategy': backup_config.strategy.value,
                        'max_retries_per_chunk': backup_config.max_retries_per_chunk,
                        'retry_delay_minutes': backup_config.retry_delay_minutes,
                        'validate_chunks': backup_config.validate_chunks
                    }
                },
                'progress': self.current_progress,
                'results': results,
                'recovery_info': {
                    'can_resume': True,
                    'next_day_to_process': self._get_next_day_to_process(),
                    'completed_days': self.current_progress.get('completed_days', []),
                    'failed_operations': self.current_progress.get('failed_chunks', [])
                }
            }

            # Write checkpoint with atomic operation (write to temp file first)
            temp_checkpoint_file = str(self.checkpoint_file) + '.tmp'
            with open(temp_checkpoint_file, 'w', encoding='utf-8') as f:
                json.dump(checkpoint_data, f, indent=2, ensure_ascii=False)

            # Atomic move to final location
            import os
            if os.path.exists(self.checkpoint_file):
                backup_file = str(self.checkpoint_file) + '.backup'
                os.rename(self.checkpoint_file, backup_file)

            os.rename(temp_checkpoint_file, self.checkpoint_file)

            logger.debug(f"Checkpoint saved successfully: {self.checkpoint_file}")

            # Log checkpoint summary
            completion = self.current_progress.get('completion_percent', 0)
            success_rate = self.current_progress.get('success_rate_percent', 0)
            logger.log_structured(
                level="DEBUG",
                component="MonthlyBackupStrategy",
                phase="CHECKPOINT",
                message=f"Checkpoint saved: {completion:.1f}% complete, {success_rate:.1f}% success rate"
            )

        except Exception as e:
            logger.error(f"Failed to save checkpoint: {str(e)}")
            # Try to clean up temp file if it exists
            try:
                temp_file = str(self.checkpoint_file) + '.tmp'
                if os.path.exists(temp_file):
                    os.remove(temp_file)
            except:
                pass  # Ignore cleanup errors

    def _get_next_day_to_process(self) -> int:
        """
        Determine the next day that needs to be processed based on current progress.

        Returns:
            Next day number to process (1-based)
        """
        completed_days = set(self.current_progress.get('completed_days', []))
        current_day = self.current_progress.get('current_day', 1)

        # Find the first day that hasn't been completed
        for day in range(1, 32):  # Maximum possible days in a month
            if day not in completed_days:
                return day

        # If all days are completed, return the day after current
        return current_day + 1

    def _should_send_progress_update(self, backup_config: MonthlyBackupConfig) -> bool:
        """
        Determine if a progress update should be sent based on time elapsed,
        completion percentage, and significant events.

        Args:
            backup_config: Monthly backup configuration

        Returns:
            True if a progress update should be sent
        """
        current_time = datetime.datetime.now()

        # Initialize last notification time if not set
        if not hasattr(self, '_last_notification_time'):
            self._last_notification_time = current_time
            return True  # Send initial notification

        time_elapsed = (current_time - self._last_notification_time).total_seconds()
        completion_percent = self.current_progress.get('completion_percent', 0)

        # Send update every 2 hours (configurable via checkpoint_interval_hours)
        notification_interval = backup_config.checkpoint_interval_hours * 3600
        if time_elapsed >= notification_interval:
            self._last_notification_time = current_time
            logger.debug(f"Sending progress update due to time interval: {time_elapsed/3600:.1f} hours")
            return True

        # Send update at significant completion milestones (every 25%)
        if not hasattr(self, '_last_notification_percent'):
            self._last_notification_percent = 0

        if completion_percent >= self._last_notification_percent + 25:
            self._last_notification_percent = int(completion_percent // 25) * 25
            self._last_notification_time = current_time
            logger.debug(f"Sending progress update due to milestone: {completion_percent:.1f}%")
            return True

        # Send update if there are new failures
        current_failures = len(self.current_progress.get('failed_chunks', []))
        if not hasattr(self, '_last_notification_failures'):
            self._last_notification_failures = 0

        if current_failures > self._last_notification_failures:
            self._last_notification_failures = current_failures
            self._last_notification_time = current_time
            logger.debug(f"Sending progress update due to new failures: {current_failures}")
            return True

        return False

    def _send_progress_notification(self, backup_config: MonthlyBackupConfig,
                                  results: Dict[str, Any]):
        """
        Send comprehensive progress notification email with detailed backup status.

        Args:
            backup_config: Monthly backup configuration
            results: Current backup results
        """
        try:
            from utils.notification import send_backup_summary_notification

            # Use comprehensive progress data
            completion_percent = self.current_progress.get('completion_percent', 0)
            success_rate = self.current_progress.get('success_rate_percent', 0)

            # Prepare comprehensive summary
            summary = {
                'type': 'monthly_backup_progress',
                'backup_mode': 'monthly',
                'month': backup_config.month,
                'year': backup_config.year,
                'strategy': backup_config.strategy.value,

                # Progress metrics
                'completion_percent': completion_percent,
                'success_rate_percent': success_rate,
                'total_tables': self.current_progress.get('total_tables', 0),
                'successful_tables': self.current_progress.get('successful_tables', 0),
                'failed_tables': self.current_progress.get('failed_tables', 0),

                # Time metrics
                'start_time': self.current_progress.get('start_time'),
                'last_update': self.current_progress.get('last_update'),
                'elapsed_time_formatted': self.current_progress.get('elapsed_time_formatted', 'unknown'),
                'estimated_remaining_formatted': self.current_progress.get('estimated_remaining_formatted', 'unknown'),
                'estimated_completion_time': self.current_progress.get('estimated_completion_time'),

                # Data metrics
                'total_rows_processed': self.current_progress.get('total_rows_processed', 0),
                'total_size_bytes': self.current_progress.get('total_size_bytes', 0),
                'completed_days': len(self.current_progress.get('completed_days', [])),
                'total_days': self._get_days_in_month(backup_config.month, backup_config.year),

                # Current operation
                'current_day': self.current_progress.get('current_day'),
                'current_table': self.current_progress.get('current_table'),
                'current_operation_status': self.current_progress.get('current_operation_status'),

                # Error information
                'failed_chunks': self.current_progress.get('failed_chunks', []),
                'recent_errors': self._get_recent_errors(),

                # Legacy compatibility
                'completed_chunks': results.get('completed_chunks', 0),
                'total_chunks': results.get('total_chunks', 0),
                'status': results.get('status', 'in_progress')
            }

            # Send notification
            success = send_backup_summary_notification(summary, self.config_manager)
            if success:
                logger.info(f"Progress notification sent: {completion_percent:.1f}% complete, "
                           f"{success_rate:.1f}% success rate")
            else:
                logger.warning("Failed to send progress notification")

        except Exception as e:
            logger.error(f"Failed to send progress notification: {str(e)}")

    def _get_recent_errors(self, limit: int = 5) -> List[Dict[str, Any]]:
        """
        Get the most recent errors for notification purposes.

        Args:
            limit: Maximum number of recent errors to return

        Returns:
            List of recent error information
        """
        failed_chunks = self.current_progress.get('failed_chunks', [])

        # Sort by timestamp (most recent first) and limit
        recent_errors = sorted(
            failed_chunks,
            key=lambda x: x.get('timestamp', ''),
            reverse=True
        )[:limit]

        return recent_errors

    def _generate_weekly_ranges(self, month: int, year: int) -> List[Tuple[datetime.date, datetime.date]]:
        """Generate weekly date ranges for the month."""
        ranges = []
        start_date = datetime.date(year, month, 1)
        days_in_month = self._get_days_in_month(month, year)

        current_date = start_date
        while current_date.month == month:
            week_end = min(
                current_date + datetime.timedelta(days=6),
                datetime.date(year, month, days_in_month)
            )
            ranges.append((current_date, week_end))
            current_date = week_end + datetime.timedelta(days=1)

        return ranges

    def _analyze_tables_for_chunking(self, table_names: List[str]) -> Dict[str, Dict[str, Any]]:
        """Analyze tables to determine optimal chunking strategy."""
        analysis = {}
        for table_name in table_names:
            # Mock analysis - in real implementation, this would analyze table characteristics
            analysis[table_name] = {
                'estimated_size': 'medium',
                'recommended_strategy': 'daily',
                'complexity': 'low'
            }
        return analysis

    def _process_week_chunk(self, backup_config: MonthlyBackupConfig, table_names: List[str],
                           start_date: datetime.date, end_date: datetime.date) -> Dict[str, Any]:
        """
        Process a weekly chunk of data using the unified table processor.
        
        Args:
            backup_config: Monthly backup configuration
            table_names: List of tables to process
            start_date: Start date of the week
            end_date: End date of the week
            
        Returns:
            Processing results for the week
        """
        logger.info(f"Processing week chunk: {start_date} to {end_date}")
        
        results = {
            'start_date': start_date.isoformat(),
            'end_date': end_date.isoformat(),
            'successful_tables': 0,
            'failed_tables': [],
            'total_rows': 0
        }
        
        # Create backup configuration for this week
        week_config = BackupConfig()
        week_config.specific_date = start_date
        week_config.days = (end_date - start_date).days + 1
        week_config.max_retries = backup_config.max_retries_per_chunk
        week_config.retry_delay = backup_config.retry_delay_minutes * 60
        week_config.timeout = backup_config.network_timeout_minutes * 60
        week_config.validate = backup_config.validate_chunks
        
        # Process each table for this week
        for table_name in table_names:
            try:
                logger.info(f"Processing {table_name} for week {start_date} to {end_date}")
                
                # Use unified table processor
                processor = UnifiedTableProcessor(week_config, strategy='smart')
                result = processor.process_tables([table_name])

                # Extract table-specific result
                table_result = result.get('table_results', {}).get(table_name, {})
                summary = result.get('summary', {})

                if result.get('status') == 'success' and table_result.get('status') == 'success':
                    results['successful_tables'] += 1
                    results['total_rows'] += table_result.get('rows_backed_up', summary.get('total_rows_backed_up', 0))
                else:
                    results['failed_tables'].append({
                        'table': table_name,
                        'error': result.get('error', 'Unknown error')
                    })
                    
            except Exception as e:
                logger.error(f"Failed to process {table_name}: {str(e)}")
                results['failed_tables'].append({
                    'table': table_name,
                    'error': str(e)
                })
                
        return results

    def _process_table_daily_chunks(self, backup_config: MonthlyBackupConfig,
                                   table_name: str) -> Dict[str, Any]:
        """
        Process a table using daily chunks for maximum reliability.
        
        Args:
            backup_config: Monthly backup configuration
            table_name: Name of the table to process
            
        Returns:
            Processing results
        """
        logger.info(f"Processing {table_name} using daily chunks")
        
        days_in_month = self._get_days_in_month(backup_config.month, backup_config.year)
        results = {
            'table': table_name,
            'status': 'success',
            'chunks_processed': 0,
            'failed_days': [],
            'total_rows': 0
        }
        
        for day in range(1, days_in_month + 1):
            try:
                day_date = datetime.date(backup_config.year, backup_config.month, day)
                day_config = self._create_day_backup_config(backup_config, day_date)
                
                # Use unified table processor for this day
                processor = UnifiedTableProcessor(day_config, strategy='smart')
                day_result = processor.process_tables([table_name])

                # Extract table-specific result
                table_result = day_result.get('table_results', {}).get(table_name, {})
                summary = day_result.get('summary', {})

                if day_result.get('status') == 'success' and table_result.get('status') == 'success':
                    results['chunks_processed'] += 1
                    results['total_rows'] += table_result.get('rows_backed_up', summary.get('total_rows_backed_up', 0))
                else:
                    results['failed_days'].append({
                        'day': day,
                        'error': day_result.get('error', 'Unknown error')
                    })
                    
            except Exception as e:
                logger.error(f"Failed to process {table_name} for day {day}: {str(e)}")
                results['failed_days'].append({
                    'day': day,
                    'error': str(e)
                })
                
        if results['failed_days']:
            results['status'] = 'partial' if results['chunks_processed'] > 0 else 'failed'
            
        return results

    def _process_table_weekly_chunks(self, backup_config: MonthlyBackupConfig,
                                    table_name: str) -> Dict[str, Any]:
        """
        Process a table using weekly chunks for balanced performance.
        
        Args:
            backup_config: Monthly backup configuration
            table_name: Name of the table to process
            
        Returns:
            Processing results
        """
        logger.info(f"Processing {table_name} using weekly chunks")
        
        # Generate weekly ranges
        week_ranges = self._generate_weekly_ranges(backup_config.month, backup_config.year)
        results = {
            'table': table_name,
            'status': 'success',
            'chunks_processed': 0,
            'failed_weeks': [],
            'total_rows': 0
        }
        
        for week_num, (start_date, end_date) in enumerate(week_ranges, 1):
            try:
                # Create config for this week
                week_config = BackupConfig()
                week_config.specific_date = start_date
                week_config.days = (end_date - start_date).days + 1
                week_config.max_retries = backup_config.max_retries_per_chunk
                week_config.retry_delay = backup_config.retry_delay_minutes * 60
                
                # Process the week
                processor = UnifiedTableProcessor(week_config, strategy='smart')
                week_result = processor.process_tables([table_name])

                # Extract table-specific result
                table_result = week_result.get('table_results', {}).get(table_name, {})
                summary = week_result.get('summary', {})

                if week_result.get('status') == 'success' and table_result.get('status') == 'success':
                    results['chunks_processed'] += 1
                    results['total_rows'] += table_result.get('rows_backed_up', summary.get('total_rows_backed_up', 0))
                else:
                    results['failed_weeks'].append({
                        'week': week_num,
                        'error': week_result.get('error', 'Unknown error')
                    })
                    
            except Exception as e:
                logger.error(f"Failed to process {table_name} for week {week_num}: {str(e)}")
                results['failed_weeks'].append({
                    'week': week_num,
                    'error': str(e)
                })
                
        if results['failed_weeks']:
            results['status'] = 'partial' if results['chunks_processed'] > 0 else 'failed'
            
        return results

    def _process_table_monthly_chunk(self, backup_config: MonthlyBackupConfig,
                                    table_name: str) -> Dict[str, Any]:
        """
        Process a table using a single monthly chunk for simple tables.
        
        Args:
            backup_config: Monthly backup configuration
            table_name: Name of the table to process
            
        Returns:
            Processing results
        """
        logger.info(f"Processing {table_name} using monthly chunk")
        
        results = {
            'table': table_name,
            'status': 'success',
            'chunks_processed': 0,
            'total_rows': 0
        }
        
        try:
            # Create config for the whole month
            month_config = BackupConfig()
            month_config.specific_date = datetime.date(backup_config.year, backup_config.month, 1)
            month_config.days = self._get_days_in_month(backup_config.month, backup_config.year)
            month_config.max_retries = backup_config.max_retries_per_chunk
            month_config.retry_delay = backup_config.retry_delay_minutes * 60
            month_config.timeout = backup_config.network_timeout_minutes * 60
            
            # Process the entire month
            processor = UnifiedTableProcessor(month_config, strategy='smart')
            result = processor.process_tables([table_name])

            # Extract table-specific result
            table_result = result.get('table_results', {}).get(table_name, {})
            summary = result.get('summary', {})

            if result.get('status') == 'success' and table_result.get('status') == 'success':
                results['chunks_processed'] = 1
                results['total_rows'] = table_result.get('rows_backed_up', summary.get('total_rows_backed_up', 0))
            else:
                results['status'] = 'failed'
                results['error'] = result.get('error', 'Unknown error')
                
        except Exception as e:
            logger.error(f"Failed to process {table_name} for month: {str(e)}")
            results['status'] = 'failed'
            results['error'] = str(e)
            
        return results
