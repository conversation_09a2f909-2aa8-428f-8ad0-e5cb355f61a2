#!/usr/bin/env python3
"""
Asynchronous Logging System for TNGD Backup System

This module provides high-performance asynchronous logging to minimize I/O overhead
and prevent backup process blocking during log operations.

Features:
- Asynchronous log message processing with queue-based buffering
- Configurable batch writing to reduce I/O operations
- Duplicate message detection and suppression
- Log level filtering and adaptive verbosity
- Performance monitoring and metrics collection
- Graceful shutdown with message flushing

Author: TNGD Backup System
Date: 2025-06-23
"""

import asyncio
import threading
import queue
import time
import logging
import logging.handlers
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from collections import defaultdict, deque
from pathlib import Path
import hashlib

from utils.enhanced_logging import get_logger

# Use standard logger for this module to avoid circular imports
logger = logging.getLogger(__name__)


@dataclass
class LogMessage:
    """Represents a log message with metadata."""
    timestamp: float
    level: int
    message: str
    component: str
    phase: str
    correlation_id: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    
    def get_hash(self) -> str:
        """Get hash for duplicate detection."""
        content = f"{self.level}:{self.component}:{self.phase}:{self.message}"
        return hashlib.md5(content.encode()).hexdigest()


class DuplicateMessageFilter:
    """Filters duplicate log messages to reduce noise."""
    
    def __init__(self, window_size: int = 100, time_window: float = 60.0):
        self.window_size = window_size
        self.time_window = time_window
        self.message_cache = deque(maxlen=window_size)
        self.message_counts = defaultdict(int)
        self.last_cleanup = time.time()
        
    def should_log(self, log_message: LogMessage) -> bool:
        """Check if message should be logged (not a duplicate)."""
        current_time = time.time()
        message_hash = log_message.get_hash()
        
        # Cleanup old messages periodically
        if current_time - self.last_cleanup > self.time_window:
            self._cleanup_old_messages(current_time)
            self.last_cleanup = current_time
        
        # Check if this is a duplicate
        if message_hash in self.message_counts:
            self.message_counts[message_hash] += 1
            
            # Log every 10th occurrence of the same message
            if self.message_counts[message_hash] % 10 == 0:
                # Modify message to indicate repetition
                log_message.message = f"{log_message.message} (repeated {self.message_counts[message_hash]} times)"
                return True
            return False
        
        # New message
        self.message_counts[message_hash] = 1
        self.message_cache.append((message_hash, current_time))
        return True
    
    def _cleanup_old_messages(self, current_time: float):
        """Remove old messages from cache."""
        cutoff_time = current_time - self.time_window
        
        # Remove old entries from cache
        while self.message_cache and self.message_cache[0][1] < cutoff_time:
            old_hash, _ = self.message_cache.popleft()
            if old_hash in self.message_counts:
                del self.message_counts[old_hash]


class AsyncLogHandler:
    """Asynchronous log handler with batching and performance optimization."""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.message_queue = queue.Queue(maxsize=config.get('queue_size', 10000))
        self.batch_size = config.get('batch_size', 100)
        self.flush_interval = config.get('flush_interval', 1.0)  # seconds
        self.duplicate_filter = DuplicateMessageFilter()
        
        # Performance tracking
        self.messages_processed = 0
        self.messages_dropped = 0
        self.batch_writes = 0
        self.last_flush = time.time()
        
        # Worker thread
        self.worker_thread = None
        self.shutdown_event = threading.Event()
        self.running = False
        
        # Log handlers
        self.handlers = {}
        self._setup_handlers()
        
    def _setup_handlers(self):
        """Setup log file handlers."""
        log_dir = Path(self.config.get('log_directory', 'logs'))
        log_dir.mkdir(exist_ok=True)
        
        # Main log file
        main_log = log_dir / 'async_backup.log'
        self.handlers['main'] = logging.handlers.RotatingFileHandler(
            main_log,
            maxBytes=self.config.get('max_file_size', 50 * 1024 * 1024),
            backupCount=self.config.get('backup_count', 5),
            encoding='utf-8'
        )
        
        # Error log file
        error_log = log_dir / 'async_errors.log'
        self.handlers['error'] = logging.handlers.RotatingFileHandler(
            error_log,
            maxBytes=self.config.get('max_file_size', 50 * 1024 * 1024),
            backupCount=self.config.get('backup_count', 5),
            encoding='utf-8'
        )
        
        # Setup formatters
        formatter = logging.Formatter(
            '%(asctime)s [%(levelname)s] %(component)s:%(phase)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        for handler in self.handlers.values():
            handler.setFormatter(formatter)
    
    def start(self):
        """Start the async logging worker."""
        if self.running:
            return
            
        self.running = True
        self.shutdown_event.clear()
        self.worker_thread = threading.Thread(target=self._worker_loop, daemon=True)
        self.worker_thread.start()
        logger.info("Async logging worker started")
    
    def stop(self, timeout: float = 5.0):
        """Stop the async logging worker."""
        if not self.running:
            return
            
        self.shutdown_event.set()
        
        # Wait for worker to finish
        if self.worker_thread:
            self.worker_thread.join(timeout=timeout)
            
        # Flush remaining messages
        self._flush_remaining_messages()
        
        self.running = False
        logger.info(f"Async logging stopped. Processed: {self.messages_processed}, "
                   f"Dropped: {self.messages_dropped}, Batches: {self.batch_writes}")
    
    def log(self, log_message: LogMessage):
        """Queue a log message for async processing."""
        if not self.running:
            return
            
        # Apply duplicate filtering
        if not self.duplicate_filter.should_log(log_message):
            return
            
        try:
            # Try to add to queue (non-blocking)
            self.message_queue.put_nowait(log_message)
        except queue.Full:
            self.messages_dropped += 1
            # Drop oldest message and add new one
            try:
                self.message_queue.get_nowait()
                self.message_queue.put_nowait(log_message)
            except queue.Empty:
                pass
    
    def _worker_loop(self):
        """Main worker loop for processing log messages."""
        message_batch = []
        
        while not self.shutdown_event.is_set() or not self.message_queue.empty():
            try:
                # Get message with timeout
                try:
                    message = self.message_queue.get(timeout=0.1)
                    message_batch.append(message)
                except queue.Empty:
                    # Check if we should flush based on time
                    if message_batch and time.time() - self.last_flush >= self.flush_interval:
                        self._write_batch(message_batch)
                        message_batch = []
                    continue
                
                # Write batch if it's full
                if len(message_batch) >= self.batch_size:
                    self._write_batch(message_batch)
                    message_batch = []
                    
            except Exception as e:
                logger.error(f"Error in async logging worker: {str(e)}")
        
        # Write remaining messages
        if message_batch:
            self._write_batch(message_batch)
    
    def _write_batch(self, messages: List[LogMessage]):
        """Write a batch of messages to log files."""
        if not messages:
            return
            
        try:
            for message in messages:
                # Choose appropriate handler
                handler = self.handlers['error'] if message.level >= logging.ERROR else self.handlers['main']
                
                # Create log record
                record = logging.LogRecord(
                    name='tngd_backup',
                    level=message.level,
                    pathname='',
                    lineno=0,
                    msg=message.message,
                    args=(),
                    exc_info=None
                )
                
                # Add custom attributes
                record.component = message.component
                record.phase = message.phase
                record.created = message.timestamp
                
                # Write to handler
                handler.emit(record)
            
            # Flush handlers
            for handler in self.handlers.values():
                handler.flush()
            
            self.messages_processed += len(messages)
            self.batch_writes += 1
            self.last_flush = time.time()
            
        except Exception as e:
            logger.error(f"Error writing log batch: {str(e)}")
    
    def _flush_remaining_messages(self):
        """Flush any remaining messages in the queue."""
        remaining_messages = []
        
        while not self.message_queue.empty():
            try:
                message = self.message_queue.get_nowait()
                remaining_messages.append(message)
            except queue.Empty:
                break
        
        if remaining_messages:
            self._write_batch(remaining_messages)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get logging performance statistics."""
        return {
            'messages_processed': self.messages_processed,
            'messages_dropped': self.messages_dropped,
            'batch_writes': self.batch_writes,
            'queue_size': self.message_queue.qsize(),
            'running': self.running
        }


class OptimizedLogger:
    """Optimized logger with async processing and duplicate filtering."""
    
    def __init__(self, name: str, config: Optional[Dict[str, Any]] = None):
        self.name = name
        self.config = config or {}
        self.async_handler = AsyncLogHandler(self.config)
        self.correlation_id = None
        
        # Start async processing
        self.async_handler.start()
    
    def set_correlation_id(self, correlation_id: str):
        """Set correlation ID for log messages."""
        self.correlation_id = correlation_id
    
    def log(self, level: int, message: str, component: str = "general", phase: str = "PROCESS", **kwargs):
        """Log a message asynchronously."""
        log_message = LogMessage(
            timestamp=time.time(),
            level=level,
            message=message,
            component=component,
            phase=phase,
            correlation_id=self.correlation_id,
            metadata=kwargs
        )
        
        self.async_handler.log(log_message)
    
    def info(self, message: str, component: str = "general", phase: str = "PROCESS", **kwargs):
        """Log info message."""
        self.log(logging.INFO, message, component, phase, **kwargs)
    
    def warning(self, message: str, component: str = "general", phase: str = "PROCESS", **kwargs):
        """Log warning message."""
        self.log(logging.WARNING, message, component, phase, **kwargs)
    
    def error(self, message: str, component: str = "general", phase: str = "PROCESS", **kwargs):
        """Log error message."""
        self.log(logging.ERROR, message, component, phase, **kwargs)
    
    def debug(self, message: str, component: str = "general", phase: str = "PROCESS", **kwargs):
        """Log debug message."""
        self.log(logging.DEBUG, message, component, phase, **kwargs)
    
    def shutdown(self, timeout: float = 5.0):
        """Shutdown the async logger."""
        self.async_handler.stop(timeout)
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics."""
        return self.async_handler.get_stats()


# Global optimized logger instance
_global_optimized_logger = None
_logger_lock = threading.Lock()


def get_optimized_logger(name: str = "tngd_backup", config: Optional[Dict[str, Any]] = None) -> OptimizedLogger:
    """Get or create optimized logger instance."""
    global _global_optimized_logger
    
    with _logger_lock:
        if _global_optimized_logger is None:
            _global_optimized_logger = OptimizedLogger(name, config)
        return _global_optimized_logger


def shutdown_optimized_logging(timeout: float = 5.0):
    """Shutdown optimized logging system."""
    global _global_optimized_logger
    
    with _logger_lock:
        if _global_optimized_logger:
            _global_optimized_logger.shutdown(timeout)
            _global_optimized_logger = None
