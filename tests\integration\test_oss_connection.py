#!/usr/bin/env python3
"""
OSS Connection Test Script
Test Alibaba Cloud OSS connectivity and list backup files

This script checks both the NEW path structure (Devo/{month}/week {n}/{date}/)
and the OLD path structure (backup/daily/{date}/) for backward compatibility.
"""

import os
import sys
from datetime import datetime

# Add the project root to the path (go up two levels from tests/integration/)
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

try:
    import oss2
    from core.config_manager import ConfigManager
except ImportError as e:
    print(f"Import error: {e}")
    print("Please ensure all dependencies are installed")
    sys.exit(1)

def test_oss_connection():
    """Test OSS connection and list backup files."""
    try:
        # Load configuration
        config = ConfigManager()
        credentials = config.get_oss_credentials()
        
        print("🔧 OSS Configuration Test")
        print("=" * 50)
        print(f"Endpoint: {credentials['endpoint']}")
        print(f"Bucket: {credentials['bucket']}")
        print(f"Prefix: {credentials['prefix']}")
        print()
        
        # Create OSS client
        auth = oss2.Auth(credentials['access_key_id'], credentials['access_key_secret'])
        bucket = oss2.Bucket(auth, credentials['endpoint'], credentials['bucket'])
        
        print("✅ OSS Authentication: SUCCESS")
        
        # Test bucket access
        try:
            bucket_info = bucket.get_bucket_info()
            print(f"✅ Bucket Access: SUCCESS")
            print(f"   Storage Class: {bucket_info.storage_class}")
            print(f"   Creation Date: {bucket_info.creation_date}")
        except Exception as e:
            print(f"❌ Bucket Access: FAILED - {e}")
            return False
        
        print()
        print("📁 Listing backup files...")
        print("=" * 50)
        
        # List files in NEW backup directory structure
        new_backup_prefix = "Devo/June/week 3/2025-06-19/"
        files_found = 0

        print(f"🔍 Checking NEW path structure: {new_backup_prefix}")
        for obj in oss2.ObjectIterator(bucket, prefix=new_backup_prefix):
            files_found += 1
            size_mb = obj.size / (1024 * 1024)
            print(f"📄 {obj.key}")
            print(f"   Size: {size_mb:.1f} MB")
            print(f"   Modified: {obj.last_modified}")
            print(f"   ETag: {obj.etag}")
            print()

        if files_found == 0:
            print(f"⚠️  No backup files found in {new_backup_prefix}")

            # Check old backup directory
            print("\n🔍 Checking OLD path structure: backup/daily/2025-06-19/")
            old_files_found = 0
            for obj in oss2.ObjectIterator(bucket, prefix="backup/daily/2025-06-19/"):
                old_files_found += 1
                size_mb = obj.size / (1024 * 1024)
                print(f"📄 {obj.key} ({size_mb:.1f} MB)")

            if old_files_found > 0:
                print(f"✅ Found {old_files_found} file(s) in OLD structure")

            # Check if there are any files in the Devo directory at all
            print("\n🔍 Checking for any files in Devo/ directory...")
            for obj in oss2.ObjectIterator(bucket, prefix="Devo/", max_keys=10):
                size_mb = obj.size / (1024 * 1024)
                print(f"📄 {obj.key} ({size_mb:.1f} MB)")
        else:
            print(f"✅ Found {files_found} backup file(s) in NEW structure for 2025-06-19")
        
        # Test specific file that should have been uploaded with NEW path structure
        new_target_file = "Devo/June/week 3/2025-06-19/my_app_tngd_waf_20250619_044648_2025-06-19.tar.gz"
        print(f"\n🎯 Checking NEW structure file: {new_target_file}")

        try:
            obj_info = bucket.head_object(new_target_file)
            size_mb = obj_info.content_length / (1024 * 1024)
            print(f"✅ NEW structure file exists!")
            print(f"   Size: {size_mb:.1f} MB")
            print(f"   Last Modified: {obj_info.last_modified}")
            print(f"   ETag: {obj_info.etag}")
            print(f"   Content Type: {obj_info.content_type}")
        except oss2.exceptions.NoSuchKey:
            print(f"❌ NEW structure file not found: {new_target_file}")
        except Exception as e:
            print(f"❌ Error checking NEW structure file: {e}")

        # Also check old file for comparison
        old_target_file = "backup/daily/2025-06-19/my_app_tngd_waf_20250619_042344.tar.gz"
        print(f"\n🔍 Checking OLD structure file: {old_target_file}")

        try:
            obj_info = bucket.head_object(old_target_file)
            size_mb = obj_info.content_length / (1024 * 1024)
            print(f"✅ OLD structure file still exists!")
            print(f"   Size: {size_mb:.1f} MB")
            print(f"   Last Modified: {obj_info.last_modified}")
            print(f"   ETag: {obj_info.etag}")
            print(f"   Content Type: {obj_info.content_type}")
        except oss2.exceptions.NoSuchKey:
            print(f"ℹ️  OLD structure file not found: {old_target_file}")
        except Exception as e:
            print(f"❌ Error checking OLD structure file: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ OSS Connection Test Failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 TNGD OSS Connection Test")
    print("=" * 60)
    print(f"Test Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    success = test_oss_connection()
    
    print()
    print("=" * 60)
    if success:
        print("✅ OSS Connection Test: PASSED")
    else:
        print("❌ OSS Connection Test: FAILED")
