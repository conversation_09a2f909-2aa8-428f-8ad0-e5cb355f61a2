#!/usr/bin/env python3
"""
OSS Path Generation Test Script
Test the ConfigManager.get_oss_path() method to verify correct path formatting
"""

import os
import sys
from datetime import datetime

# Add the project root to the path (go up two levels from tests/integration/)
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

try:
    from core.config_manager import ConfigManager
except ImportError as e:
    print(f"Import error: {e}")
    print("Please ensure all dependencies are installed")
    sys.exit(1)

def test_oss_path_generation():
    """Test OSS path generation with different scenarios."""
    try:
        # Initialize ConfigManager
        config = ConfigManager()
        
        print("🧪 OSS Path Generation Test")
        print("=" * 60)
        print(f"Test Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # Test scenarios
        test_cases = [
            {
                'name': 'Current Date - WAF Table',
                'table_name': 'my_app_tngd_waf_20250619_042344',
                'date': datetime.now(),
                'algorithm': 'tar.gz'
            },
            {
                'name': 'Current Date - ActionTrail Table',
                'table_name': 'my_app_tngd_actiontraillinux_20250619_040419',
                'date': datetime.now(),
                'algorithm': 'tar.gz'
            },
            {
                'name': 'Specific Date - June 19, 2025',
                'table_name': 'my_app_tngd_waf_20250619_123456',
                'date': datetime(2025, 6, 19, 12, 34, 56),
                'algorithm': 'tar.gz'
            },
            {
                'name': 'Different Month - May 15, 2025',
                'table_name': 'my_app_tngd_test_20250515_090000',
                'date': datetime(2025, 5, 15, 9, 0, 0),
                'algorithm': 'tar.gz'
            },
            {
                'name': 'Different Week - June 1, 2025 (Week 1)',
                'table_name': 'my_app_tngd_test_20250601_120000',
                'date': datetime(2025, 6, 1, 12, 0, 0),
                'algorithm': 'tar.gz'
            },
            {
                'name': 'Different Week - June 25, 2025 (Week 4)',
                'table_name': 'my_app_tngd_test_20250625_150000',
                'date': datetime(2025, 6, 25, 15, 0, 0),
                'algorithm': 'tar.gz'
            }
        ]
        
        print("📋 Test Cases:")
        print("-" * 60)
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n{i}. {test_case['name']}")
            print(f"   Table: {test_case['table_name']}")
            print(f"   Date: {test_case['date'].strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"   Algorithm: {test_case['algorithm']}")
            
            try:
                # Generate OSS path
                oss_path = config.get_oss_path(
                    table_name=test_case['table_name'],
                    date=test_case['date'],
                    algorithm=test_case['algorithm']
                )
                
                print(f"   ✅ Generated Path: {oss_path}")
                
                # Analyze path components
                path_parts = oss_path.split('/')
                if len(path_parts) >= 4:
                    print(f"   📁 Structure Analysis:")
                    print(f"      - Root: {path_parts[0]}")
                    print(f"      - Month: {path_parts[1]}")
                    print(f"      - Week: {path_parts[2]}")
                    print(f"      - Date: {path_parts[3]}")
                    if len(path_parts) > 4:
                        print(f"      - File: {path_parts[4]}")
                
            except Exception as e:
                print(f"   ❌ Error: {e}")
        
        print("\n" + "=" * 60)
        print("🔍 Configuration Analysis:")
        print("-" * 60)
        
        # Show current configuration
        template = config.get('storage', 'oss_path_template', 'Not found')
        print(f"Template: {template}")
        
        oss_prefix = config.get_env('OSS_PREFIX', 'devo/backups/')
        print(f"OSS Prefix: {oss_prefix}")
        
        compression_algo = config.get('storage', 'compression_algorithm', 'zip')
        print(f"Default Compression: {compression_algo}")
        
        print("\n" + "=" * 60)
        print("📊 Expected vs Actual Path Format:")
        print("-" * 60)
        
        # Show expected format
        current_date = datetime.now()
        month_name = current_date.strftime('%B')
        week_number = (current_date.day - 1) // 7 + 1
        date_str = current_date.strftime('%Y-%m-%d')
        
        print(f"Expected Format: Devo/{month_name}/week {week_number}/{date_str}/table_name.tar.gz")
        
        # Generate actual path for comparison
        test_table = "my_app_tngd_waf_20250619_123456"
        actual_path = config.get_oss_path(test_table, current_date, 'tar.gz')
        print(f"Actual Generated: {actual_path}")
        
        # Validate format
        expected_components = ['Devo', month_name, f'week {week_number}', date_str]
        actual_components = actual_path.split('/')
        
        print(f"\n🔍 Component Validation:")
        for i, (expected, actual) in enumerate(zip(expected_components, actual_components[:4])):
            status = "✅" if expected == actual else "❌"
            print(f"   {status} Component {i+1}: Expected '{expected}', Got '{actual}'")
        
        # Check file extension
        if actual_path.endswith('.tar.gz'):
            print(f"   ✅ File Extension: Correct (.tar.gz)")
        else:
            print(f"   ❌ File Extension: Expected .tar.gz, Got {actual_path.split('.')[-1]}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test Failed: {e}")
        return False

def test_path_consistency():
    """Test that path generation is consistent across multiple calls."""
    print("\n" + "=" * 60)
    print("🔄 Path Consistency Test:")
    print("-" * 60)
    
    try:
        config = ConfigManager()
        test_date = datetime(2025, 6, 19, 12, 0, 0)
        table_name = "test_table_consistency"
        
        # Generate path multiple times
        paths = []
        for i in range(5):
            path = config.get_oss_path(table_name, test_date, 'tar.gz')
            paths.append(path)
            print(f"   Call {i+1}: {path}")
        
        # Check consistency
        if all(path == paths[0] for path in paths):
            print(f"   ✅ Consistency: All paths are identical")
            return True
        else:
            print(f"   ❌ Consistency: Paths differ between calls")
            return False
            
    except Exception as e:
        print(f"   ❌ Consistency Test Failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 TNGD OSS Path Generation Test Suite")
    print("=" * 80)
    
    # Run main test
    success1 = test_oss_path_generation()
    
    # Run consistency test
    success2 = test_path_consistency()
    
    print("\n" + "=" * 80)
    if success1 and success2:
        print("✅ All Tests PASSED - OSS Path Generation Working Correctly!")
    else:
        print("❌ Some Tests FAILED - Please Review Configuration")
    print("=" * 80)
