#!/usr/bin/env python3
"""
Log Security and Compliance Features for TNGD Backup System

This module provides advanced security features for logging:
- Log integrity verification with cryptographic signatures
- Secure log file permissions management
- Audit trail generation and verification
- Compliance reporting and log retention management
- Tamper detection for critical log files

Security Features:
- HMAC-based log integrity verification
- Secure file permissions (600 for files, 700 for directories)
- Audit trail with cryptographic signatures
- Automated compliance reporting
- Log retention policy enforcement
"""

import os
import stat
import hmac
import hashlib
import json
import datetime
import secrets
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum


class SecurityLevel(Enum):
    """Security levels for log files."""
    BASIC = "basic"           # Standard logging with basic security
    ENHANCED = "enhanced"     # Enhanced security with integrity checks
    AUDIT = "audit"          # Full audit compliance with signatures
    FORENSIC = "forensic"    # Forensic-level security for investigations


@dataclass
class LogIntegrityRecord:
    """Record for log file integrity verification."""
    file_path: str
    timestamp: datetime.datetime
    file_size: int
    checksum: str
    signature: str
    security_level: SecurityLevel
    metadata: Dict[str, Any] = field(default_factory=dict)


class LogSecurityManager:
    """
    Manages security features for log files including integrity verification,
    secure permissions, and compliance reporting.
    """

    def __init__(self, secret_key: Optional[str] = None, security_level: SecurityLevel = SecurityLevel.ENHANCED):
        """
        Initialize the log security manager.

        Args:
            secret_key: Secret key for HMAC signatures (generated if not provided)
            security_level: Default security level for operations
        """
        self.secret_key = secret_key or self._generate_secret_key()
        self.security_level = security_level
        self.integrity_records: Dict[str, LogIntegrityRecord] = {}
        self._load_integrity_records()

    def _generate_secret_key(self) -> str:
        """Generate a secure secret key for HMAC operations."""
        return secrets.token_hex(32)

    def _load_integrity_records(self):
        """Load existing integrity records from storage."""
        integrity_file = Path("logs/.integrity_records.json")
        if integrity_file.exists():
            try:
                with open(integrity_file, 'r') as f:
                    data = json.load(f)
                    for record_data in data.get('records', []):
                        record = LogIntegrityRecord(
                            file_path=record_data['file_path'],
                            timestamp=datetime.datetime.fromisoformat(record_data['timestamp']),
                            file_size=record_data['file_size'],
                            checksum=record_data['checksum'],
                            signature=record_data['signature'],
                            security_level=SecurityLevel(record_data['security_level']),
                            metadata=record_data.get('metadata', {})
                        )
                        self.integrity_records[record_data['file_path']] = record
            except Exception as e:
                print(f"Warning: Could not load integrity records: {e}")

    def _save_integrity_records(self):
        """Save integrity records to storage."""
        integrity_file = Path("logs/.integrity_records.json")
        integrity_file.parent.mkdir(exist_ok=True)

        data = {
            'version': '1.0',
            'created': datetime.datetime.now().isoformat(),
            'records': []
        }

        for record in self.integrity_records.values():
            data['records'].append({
                'file_path': record.file_path,
                'timestamp': record.timestamp.isoformat(),
                'file_size': record.file_size,
                'checksum': record.checksum,
                'signature': record.signature,
                'security_level': record.security_level.value,
                'metadata': record.metadata
            })

        with open(integrity_file, 'w') as f:
            json.dump(data, f, indent=2)

        # Secure the integrity file itself
        self.secure_file_permissions(integrity_file)

    def secure_file_permissions(self, file_path: Path) -> bool:
        """
        Set secure permissions on a log file.

        Args:
            file_path: Path to the file to secure

        Returns:
            True if permissions were set successfully
        """
        try:
            if file_path.is_file():
                # Set file permissions to 600 (read/write for owner only)
                os.chmod(file_path, stat.S_IRUSR | stat.S_IWUSR)
            elif file_path.is_dir():
                # Set directory permissions to 700 (read/write/execute for owner only)
                os.chmod(file_path, stat.S_IRWXU)

            return True

        except Exception as e:
            print(f"Warning: Could not set secure permissions on {file_path}: {e}")
            return False

    def secure_log_directory(self, log_dir: Path) -> int:
        """
        Secure all files and directories in the log directory.

        Args:
            log_dir: Path to the log directory

        Returns:
            Number of files/directories secured
        """
        secured_count = 0

        try:
            # Secure the main directory
            if self.secure_file_permissions(log_dir):
                secured_count += 1

            # Recursively secure all files and subdirectories
            for item in log_dir.rglob("*"):
                if self.secure_file_permissions(item):
                    secured_count += 1

            return secured_count

        except Exception as e:
            print(f"Error securing log directory {log_dir}: {e}")
            return secured_count

    def calculate_file_checksum(self, file_path: Path) -> str:
        """
        Calculate SHA-256 checksum of a file.

        Args:
            file_path: Path to the file

        Returns:
            Hexadecimal checksum string
        """
        sha256_hash = hashlib.sha256()

        try:
            with open(file_path, "rb") as f:
                # Read file in chunks to handle large files
                for chunk in iter(lambda: f.read(4096), b""):
                    sha256_hash.update(chunk)

            return sha256_hash.hexdigest()

        except Exception as e:
            print(f"Error calculating checksum for {file_path}: {e}")
            return ""

    def create_integrity_signature(self, file_path: Path, checksum: str, metadata: Optional[Dict[str, Any]] = None) -> str:
        """
        Create HMAC signature for file integrity verification.

        Args:
            file_path: Path to the file
            checksum: File checksum
            metadata: Optional metadata to include in signature

        Returns:
            HMAC signature string
        """
        # Create message for signing
        message_parts = [
            str(file_path),
            checksum,
            datetime.datetime.now().isoformat()
        ]

        if metadata:
            message_parts.append(json.dumps(metadata, sort_keys=True))

        message = "|".join(message_parts).encode('utf-8')

        # Create HMAC signature
        signature = hmac.new(
            self.secret_key.encode('utf-8'),
            message,
            hashlib.sha256
        ).hexdigest()

        return signature

    def verify_file_integrity(self, file_path: Path) -> Tuple[bool, str]:
        """
        Verify the integrity of a log file.

        Args:
            file_path: Path to the file to verify

        Returns:
            Tuple of (is_valid, message)
        """
        file_path_str = str(file_path)

        # Check if we have an integrity record for this file
        if file_path_str not in self.integrity_records:
            return False, "No integrity record found for file"

        record = self.integrity_records[file_path_str]

        # Check if file exists
        if not file_path.exists():
            return False, "File does not exist"

        # Check file size
        current_size = file_path.stat().st_size
        if current_size != record.file_size:
            return False, f"File size mismatch: expected {record.file_size}, got {current_size}"

        # Calculate current checksum
        current_checksum = self.calculate_file_checksum(file_path)
        if current_checksum != record.checksum:
            return False, "File checksum mismatch - file may have been tampered with"

        # Verify signature
        expected_signature = self.create_integrity_signature(file_path, current_checksum, record.metadata)
        if not hmac.compare_digest(record.signature, expected_signature):
            return False, "Integrity signature verification failed"

        return True, "File integrity verified successfully"

    def create_integrity_record(self, file_path: Path, metadata: Optional[Dict[str, Any]] = None) -> bool:
        """
        Create an integrity record for a log file.

        Args:
            file_path: Path to the file
            metadata: Optional metadata to include

        Returns:
            True if record was created successfully
        """
        try:
            if not file_path.exists():
                print(f"Cannot create integrity record: file {file_path} does not exist")
                return False

            # Calculate file properties
            file_size = file_path.stat().st_size
            checksum = self.calculate_file_checksum(file_path)
            signature = self.create_integrity_signature(file_path, checksum, metadata)

            # Create integrity record
            record = LogIntegrityRecord(
                file_path=str(file_path),
                timestamp=datetime.datetime.now(),
                file_size=file_size,
                checksum=checksum,
                signature=signature,
                security_level=self.security_level,
                metadata=metadata or {}
            )

            # Store record
            self.integrity_records[str(file_path)] = record
            self._save_integrity_records()

            return True

        except Exception as e:
            print(f"Error creating integrity record for {file_path}: {e}")
            return False

    def generate_compliance_report(self, output_file: Optional[Path] = None) -> Dict[str, Any]:
        """
        Generate a compliance report for all log files.

        Args:
            output_file: Optional file to save the report

        Returns:
            Compliance report dictionary
        """
        report = {
            'generated_at': datetime.datetime.now().isoformat(),
            'security_level': self.security_level.value,
            'total_files': len(self.integrity_records),
            'files': [],
            'summary': {
                'verified': 0,
                'failed': 0,
                'missing': 0
            }
        }

        # Check each file
        for file_path_str, record in self.integrity_records.items():
            file_path = Path(file_path_str)
            is_valid, message = self.verify_file_integrity(file_path)

            file_report = {
                'file_path': file_path_str,
                'security_level': record.security_level.value,
                'created': record.timestamp.isoformat(),
                'file_size': record.file_size,
                'integrity_status': 'VERIFIED' if is_valid else 'FAILED',
                'message': message,
                'metadata': record.metadata
            }

            report['files'].append(file_report)

            # Update summary
            if is_valid:
                report['summary']['verified'] += 1
            elif not file_path.exists():
                report['summary']['missing'] += 1
            else:
                report['summary']['failed'] += 1

        # Save report if requested
        if output_file:
            try:
                with open(output_file, 'w') as f:
                    json.dump(report, f, indent=2)
                self.secure_file_permissions(output_file)
            except Exception as e:
                print(f"Error saving compliance report: {e}")

        return report

    def enforce_retention_policy(self, log_dir: Path, retention_days: int = 30) -> Dict[str, Any]:
        """
        Enforce log retention policy by removing old files.

        Args:
            log_dir: Log directory to clean
            retention_days: Number of days to retain logs

        Returns:
            Dictionary with cleanup results
        """
        cutoff_date = datetime.datetime.now() - datetime.timedelta(days=retention_days)
        results = {
            'cutoff_date': cutoff_date.isoformat(),
            'files_removed': 0,
            'files_archived': 0,
            'errors': []
        }

        try:
            for log_file in log_dir.rglob("*.log"):
                if log_file.is_file():
                    # Check file modification time
                    file_mtime = datetime.datetime.fromtimestamp(log_file.stat().st_mtime)

                    if file_mtime < cutoff_date:
                        try:
                            # Remove integrity record if exists
                            file_path_str = str(log_file)
                            if file_path_str in self.integrity_records:
                                del self.integrity_records[file_path_str]

                            # Remove the file
                            log_file.unlink()
                            results['files_removed'] += 1

                        except Exception as e:
                            results['errors'].append(f"Error removing {log_file}: {e}")

            # Save updated integrity records
            if results['files_removed'] > 0:
                self._save_integrity_records()

        except Exception as e:
            results['errors'].append(f"Error during retention policy enforcement: {e}")

        return results


class LogAuditor:
    """
    Provides audit trail functionality for log operations.
    """

    def __init__(self, security_manager: LogSecurityManager):
        """
        Initialize the log auditor.

        Args:
            security_manager: Security manager instance
        """
        self.security_manager = security_manager
        self.audit_log_path = Path("logs/security/audit.log")
        self.audit_log_path.parent.mkdir(parents=True, exist_ok=True)

    def log_security_event(self, event_type: str, details: Dict[str, Any], severity: str = "INFO"):
        """
        Log a security-related event to the audit trail.

        Args:
            event_type: Type of security event
            details: Event details
            severity: Event severity (INFO, WARNING, ERROR, CRITICAL)
        """
        audit_entry = {
            'timestamp': datetime.datetime.now().isoformat(),
            'event_type': event_type,
            'severity': severity,
            'details': details,
            'system_info': {
                'user': os.getenv('USER', 'unknown'),
                'hostname': os.getenv('HOSTNAME', 'unknown')
            }
        }

        try:
            with open(self.audit_log_path, 'a', encoding='utf-8') as f:
                f.write(json.dumps(audit_entry) + '\n')

            # Secure the audit log
            self.security_manager.secure_file_permissions(self.audit_log_path)

        except Exception as e:
            print(f"Error writing to audit log: {e}")

    def log_integrity_check(self, file_path: Path, result: bool, message: str):
        """Log an integrity check event."""
        self.log_security_event(
            'INTEGRITY_CHECK',
            {
                'file_path': str(file_path),
                'result': 'PASS' if result else 'FAIL',
                'message': message
            },
            'INFO' if result else 'WARNING'
        )

    def log_permission_change(self, file_path: Path, success: bool):
        """Log a file permission change event."""
        self.log_security_event(
            'PERMISSION_CHANGE',
            {
                'file_path': str(file_path),
                'success': success
            },
            'INFO' if success else 'ERROR'
        )

    def log_retention_enforcement(self, results: Dict[str, Any]):
        """Log retention policy enforcement."""
        self.log_security_event(
            'RETENTION_ENFORCEMENT',
            results,
            'WARNING' if results.get('errors') else 'INFO'
        )


# Utility functions for easy integration
def secure_log_file(file_path: Path, security_level: SecurityLevel = SecurityLevel.ENHANCED) -> bool:
    """
    Secure a single log file with integrity verification.

    Args:
        file_path: Path to the log file
        security_level: Security level to apply

    Returns:
        True if file was secured successfully
    """
    security_manager = LogSecurityManager(security_level=security_level)

    # Set secure permissions
    if not security_manager.secure_file_permissions(file_path):
        return False

    # Create integrity record
    if not security_manager.create_integrity_record(file_path):
        return False

    return True


def verify_log_integrity(file_path: Path) -> Tuple[bool, str]:
    """
    Verify the integrity of a log file.

    Args:
        file_path: Path to the log file

    Returns:
        Tuple of (is_valid, message)
    """
    security_manager = LogSecurityManager()
    return security_manager.verify_file_integrity(file_path)


def generate_security_report(output_file: Optional[Path] = None) -> Dict[str, Any]:
    """
    Generate a comprehensive security report for all log files.

    Args:
        output_file: Optional file to save the report

    Returns:
        Security report dictionary
    """
    security_manager = LogSecurityManager()
    return security_manager.generate_compliance_report(output_file)