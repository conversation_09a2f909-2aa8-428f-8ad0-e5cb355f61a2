#!/usr/bin/env python3
"""
Performance Optimizer for TNGD Backup System

This module provides comprehensive performance monitoring, metrics collection,
and adaptive optimization for backup operations.

Features:
- Real-time performance metrics collection
- Adaptive configuration based on system resources
- Performance benchmarking and profiling
- Optimization recommendations
- Historical performance tracking
- Resource usage optimization

Author: TNGD Backup System
Date: 2025-06-23
"""

import time
import threading
import psutil
import json
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from pathlib import Path
import statistics

from core.backup_config import BackupConfig
from core.performance_monitor import PerformanceMonitor
from utils.enhanced_logging import get_logger

logger = get_logger(__name__)


@dataclass
class PerformanceMetrics:
    """Performance metrics for backup operations."""
    timestamp: datetime
    operation_type: str
    duration: float
    tables_processed: int
    rows_processed: int
    data_size_mb: float
    cpu_usage_percent: float
    memory_usage_percent: float
    disk_io_mb_per_sec: float
    network_io_mb_per_sec: float
    concurrent_operations: int
    error_count: int
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PerformanceMetrics':
        """Create from dictionary."""
        data['timestamp'] = datetime.fromisoformat(data['timestamp'])
        return cls(**data)


@dataclass
class OptimizationRecommendation:
    """Optimization recommendation based on performance analysis."""
    category: str
    priority: str  # HIGH, MEDIUM, LOW
    description: str
    current_value: Any
    recommended_value: Any
    expected_improvement: str
    implementation_effort: str  # LOW, MEDIUM, HIGH


class PerformanceProfiler:
    """Profiles backup operations to identify bottlenecks."""
    
    def __init__(self):
        self.profiles = {}
        self.current_profile = None
        self.start_time = None
        
    def start_profiling(self, operation_name: str):
        """Start profiling an operation."""
        self.current_profile = {
            'operation_name': operation_name,
            'start_time': time.time(),
            'checkpoints': [],
            'resource_samples': []
        }
        self.start_time = time.time()
        logger.debug(f"Started profiling: {operation_name}")
    
    def checkpoint(self, checkpoint_name: str, metadata: Optional[Dict[str, Any]] = None):
        """Add a checkpoint to the current profile."""
        if not self.current_profile:
            return
            
        current_time = time.time()
        checkpoint = {
            'name': checkpoint_name,
            'timestamp': current_time,
            'elapsed': current_time - self.start_time,
            'metadata': metadata or {}
        }
        
        # Sample system resources
        try:
            resource_sample = {
                'cpu_percent': psutil.cpu_percent(interval=0.1),
                'memory_percent': psutil.virtual_memory().percent,
                'disk_io': psutil.disk_io_counters()._asdict() if psutil.disk_io_counters() else {},
                'network_io': psutil.net_io_counters()._asdict() if psutil.net_io_counters() else {}
            }
            checkpoint['resources'] = resource_sample
        except Exception as e:
            logger.warning(f"Failed to sample resources at checkpoint {checkpoint_name}: {str(e)}")
        
        self.current_profile['checkpoints'].append(checkpoint)
        logger.debug(f"Checkpoint: {checkpoint_name} at {checkpoint['elapsed']:.2f}s")
    
    def end_profiling(self) -> Dict[str, Any]:
        """End profiling and return results."""
        if not self.current_profile:
            return {}
            
        end_time = time.time()
        self.current_profile['end_time'] = end_time
        self.current_profile['total_duration'] = end_time - self.current_profile['start_time']
        
        # Store profile
        operation_name = self.current_profile['operation_name']
        self.profiles[operation_name] = self.current_profile.copy()
        
        result = self.current_profile.copy()
        self.current_profile = None
        
        logger.info(f"Completed profiling: {operation_name} in {result['total_duration']:.2f}s")
        return result
    
    def get_bottlenecks(self, profile_name: str) -> List[Dict[str, Any]]:
        """Identify bottlenecks in a profile."""
        if profile_name not in self.profiles:
            return []
            
        profile = self.profiles[profile_name]
        checkpoints = profile.get('checkpoints', [])
        
        if len(checkpoints) < 2:
            return []
        
        bottlenecks = []
        
        # Find slow checkpoints (taking more than 20% of total time)
        total_duration = profile['total_duration']
        threshold = total_duration * 0.2
        
        for i in range(1, len(checkpoints)):
            prev_checkpoint = checkpoints[i-1]
            curr_checkpoint = checkpoints[i]
            
            duration = curr_checkpoint['elapsed'] - prev_checkpoint['elapsed']
            
            if duration > threshold:
                bottlenecks.append({
                    'checkpoint': curr_checkpoint['name'],
                    'duration': duration,
                    'percentage': (duration / total_duration) * 100,
                    'resources': curr_checkpoint.get('resources', {})
                })
        
        return sorted(bottlenecks, key=lambda x: x['duration'], reverse=True)


class AdaptiveOptimizer:
    """Adaptive optimizer that adjusts configuration based on performance."""
    
    def __init__(self, config: BackupConfig):
        self.config = config
        self.performance_history = []
        self.optimization_history = []
        
    def analyze_performance(self, metrics: List[PerformanceMetrics]) -> List[OptimizationRecommendation]:
        """Analyze performance metrics and generate recommendations."""
        if not metrics:
            return []
            
        recommendations = []
        
        # Analyze CPU usage
        cpu_usage = [m.cpu_usage_percent for m in metrics]
        avg_cpu = statistics.mean(cpu_usage)
        
        if avg_cpu > 80:
            recommendations.append(OptimizationRecommendation(
                category="Concurrency",
                priority="HIGH",
                description="High CPU usage detected, consider reducing parallel processing",
                current_value=self.config.max_threads,
                recommended_value=max(1, self.config.max_threads - 2),
                expected_improvement="10-20% reduction in CPU usage",
                implementation_effort="LOW"
            ))
        elif avg_cpu < 40 and self.config.max_threads < 8:
            recommendations.append(OptimizationRecommendation(
                category="Concurrency",
                priority="MEDIUM",
                description="Low CPU usage, can increase parallel processing",
                current_value=self.config.max_threads,
                recommended_value=min(8, self.config.max_threads + 2),
                expected_improvement="20-30% faster processing",
                implementation_effort="LOW"
            ))
        
        # Analyze memory usage
        memory_usage = [m.memory_usage_percent for m in metrics]
        avg_memory = statistics.mean(memory_usage)
        
        if avg_memory > 85:
            recommendations.append(OptimizationRecommendation(
                category="Memory",
                priority="HIGH",
                description="High memory usage, reduce chunk size",
                current_value=self.config.chunk_size,
                recommended_value=max(1000, self.config.chunk_size // 2),
                expected_improvement="30-40% reduction in memory usage",
                implementation_effort="LOW"
            ))
        
        # Analyze processing efficiency
        processing_rates = []
        for m in metrics:
            if m.duration > 0:
                rate = m.rows_processed / m.duration
                processing_rates.append(rate)
        
        if processing_rates:
            avg_rate = statistics.mean(processing_rates)
            if avg_rate < 1000:  # Less than 1000 rows/second
                recommendations.append(OptimizationRecommendation(
                    category="Processing",
                    priority="MEDIUM",
                    description="Low processing rate, consider optimizing queries or increasing chunk size",
                    current_value=self.config.chunk_size,
                    recommended_value=min(10000000, self.config.chunk_size * 2),
                    expected_improvement="50-100% faster processing",
                    implementation_effort="MEDIUM"
                ))
        
        return recommendations
    
    def apply_optimization(self, recommendation: OptimizationRecommendation) -> bool:
        """Apply an optimization recommendation."""
        try:
            if recommendation.category == "Concurrency":
                if hasattr(self.config, 'max_threads'):
                    old_value = self.config.max_threads
                    self.config.max_threads = recommendation.recommended_value
                    logger.info(f"Applied optimization: max_threads {old_value} -> {recommendation.recommended_value}")
                    return True
            
            elif recommendation.category == "Memory":
                if hasattr(self.config, 'chunk_size'):
                    old_value = self.config.chunk_size
                    self.config.chunk_size = recommendation.recommended_value
                    logger.info(f"Applied optimization: chunk_size {old_value} -> {recommendation.recommended_value}")
                    return True
            
            elif recommendation.category == "Processing":
                if hasattr(self.config, 'chunk_size'):
                    old_value = self.config.chunk_size
                    self.config.chunk_size = recommendation.recommended_value
                    logger.info(f"Applied optimization: chunk_size {old_value} -> {recommendation.recommended_value}")
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to apply optimization: {str(e)}")
            return False


class PerformanceOptimizer:
    """Main performance optimizer class."""
    
    def __init__(self, config: BackupConfig):
        self.config = config
        self.performance_monitor = PerformanceMonitor(config.config_manager)
        self.profiler = PerformanceProfiler()
        self.adaptive_optimizer = AdaptiveOptimizer(config)
        self.metrics_history = []
        
        # Performance tracking
        self.optimization_enabled = config.config_manager.get('performance', 'optimization_enabled', True)
        self.metrics_collection_interval = config.config_manager.get('performance', 'metrics_interval', 5.0)
        self.auto_optimization = config.config_manager.get('performance', 'auto_optimization', False)
        
        # Storage
        self.metrics_file = Path("performance_metrics.json")
        self.load_historical_metrics()
        
        logger.info(f"Performance optimizer initialized (optimization: {self.optimization_enabled}, auto: {self.auto_optimization})")
    
    def start_operation_monitoring(self, operation_name: str):
        """Start monitoring a backup operation."""
        if self.optimization_enabled:
            self.profiler.start_profiling(operation_name)
            self.performance_monitor.start_monitoring()
    
    def checkpoint(self, checkpoint_name: str, metadata: Optional[Dict[str, Any]] = None):
        """Add a performance checkpoint."""
        if self.optimization_enabled:
            self.profiler.checkpoint(checkpoint_name, metadata)
    
    def end_operation_monitoring(self, operation_name: str, tables_processed: int = 0, 
                               rows_processed: int = 0, data_size_mb: float = 0.0) -> Dict[str, Any]:
        """End monitoring and collect metrics."""
        if not self.optimization_enabled:
            return {}
            
        # End profiling
        profile = self.profiler.end_profiling()
        
        # Stop performance monitoring
        self.performance_monitor.stop_monitoring()
        
        # Collect final metrics
        try:
            metrics = PerformanceMetrics(
                timestamp=datetime.now(),
                operation_type=operation_name,
                duration=profile.get('total_duration', 0),
                tables_processed=tables_processed,
                rows_processed=rows_processed,
                data_size_mb=data_size_mb,
                cpu_usage_percent=psutil.cpu_percent(interval=0.1),
                memory_usage_percent=psutil.virtual_memory().percent,
                disk_io_mb_per_sec=0.0,  # Could be calculated from profile
                network_io_mb_per_sec=0.0,  # Could be calculated from profile
                concurrent_operations=1,  # Could be tracked
                error_count=0  # Could be tracked
            )
            
            self.metrics_history.append(metrics)
            self.save_metrics()
            
            # Generate optimization recommendations if enabled
            recommendations = []
            if self.auto_optimization and len(self.metrics_history) >= 3:
                recent_metrics = self.metrics_history[-3:]
                recommendations = self.adaptive_optimizer.analyze_performance(recent_metrics)
                
                # Apply high-priority recommendations automatically
                for rec in recommendations:
                    if rec.priority == "HIGH":
                        self.adaptive_optimizer.apply_optimization(rec)
            
            return {
                'profile': profile,
                'metrics': metrics.to_dict(),
                'recommendations': [asdict(r) for r in recommendations],
                'bottlenecks': self.profiler.get_bottlenecks(operation_name)
            }
            
        except Exception as e:
            logger.error(f"Error collecting performance metrics: {str(e)}")
            return {'profile': profile}
    
    def get_performance_report(self) -> Dict[str, Any]:
        """Generate comprehensive performance report."""
        if not self.metrics_history:
            return {'status': 'no_data'}
        
        recent_metrics = self.metrics_history[-10:]  # Last 10 operations
        
        # Calculate averages
        avg_duration = statistics.mean([m.duration for m in recent_metrics])
        avg_cpu = statistics.mean([m.cpu_usage_percent for m in recent_metrics])
        avg_memory = statistics.mean([m.memory_usage_percent for m in recent_metrics])
        total_rows = sum([m.rows_processed for m in recent_metrics])
        total_tables = sum([m.tables_processed for m in recent_metrics])
        
        # Processing rate
        processing_rate = total_rows / sum([m.duration for m in recent_metrics if m.duration > 0])
        
        return {
            'status': 'success',
            'summary': {
                'operations_analyzed': len(recent_metrics),
                'avg_duration_seconds': avg_duration,
                'avg_cpu_usage_percent': avg_cpu,
                'avg_memory_usage_percent': avg_memory,
                'total_rows_processed': total_rows,
                'total_tables_processed': total_tables,
                'processing_rate_rows_per_second': processing_rate
            },
            'recommendations': [asdict(r) for r in self.adaptive_optimizer.analyze_performance(recent_metrics)],
            'historical_data': [m.to_dict() for m in recent_metrics]
        }
    
    def load_historical_metrics(self):
        """Load historical performance metrics."""
        try:
            if self.metrics_file.exists():
                with open(self.metrics_file, 'r') as f:
                    data = json.load(f)
                    self.metrics_history = [PerformanceMetrics.from_dict(m) for m in data]
                logger.info(f"Loaded {len(self.metrics_history)} historical performance metrics")
        except Exception as e:
            logger.warning(f"Failed to load historical metrics: {str(e)}")
            self.metrics_history = []
    
    def save_metrics(self):
        """Save performance metrics to file."""
        try:
            # Keep only last 100 metrics to prevent file from growing too large
            recent_metrics = self.metrics_history[-100:]
            data = [m.to_dict() for m in recent_metrics]
            
            with open(self.metrics_file, 'w') as f:
                json.dump(data, f, indent=2)
                
        except Exception as e:
            logger.error(f"Failed to save performance metrics: {str(e)}")
    
    def optimize_configuration(self) -> Dict[str, Any]:
        """Optimize configuration based on historical performance."""
        if len(self.metrics_history) < 5:
            return {'status': 'insufficient_data', 'message': 'Need at least 5 operations for optimization'}
        
        recommendations = self.adaptive_optimizer.analyze_performance(self.metrics_history[-10:])
        applied_optimizations = []
        
        for rec in recommendations:
            if rec.priority in ['HIGH', 'MEDIUM']:
                if self.adaptive_optimizer.apply_optimization(rec):
                    applied_optimizations.append(asdict(rec))
        
        return {
            'status': 'success',
            'applied_optimizations': applied_optimizations,
            'all_recommendations': [asdict(r) for r in recommendations]
        }


class PerformanceTester:
    """Performance testing framework for backup operations."""

    def __init__(self, config: BackupConfig):
        self.config = config
        self.optimizer = PerformanceOptimizer(config)

    def run_performance_test(self, test_tables: List[str], test_scenarios: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Run comprehensive performance tests."""
        results = {
            'test_start': datetime.now().isoformat(),
            'scenarios': [],
            'summary': {}
        }

        for i, scenario in enumerate(test_scenarios):
            logger.info(f"Running performance test scenario {i+1}/{len(test_scenarios)}: {scenario.get('name', 'Unnamed')}")

            # Apply scenario configuration
            original_config = self._backup_config()
            self._apply_scenario_config(scenario)

            try:
                # Run test
                scenario_result = self._run_scenario_test(test_tables, scenario)
                scenario_result['scenario'] = scenario
                results['scenarios'].append(scenario_result)

            finally:
                # Restore original configuration
                self._restore_config(original_config)

        # Generate summary
        results['summary'] = self._generate_test_summary(results['scenarios'])
        results['test_end'] = datetime.now().isoformat()

        return results

    def _backup_config(self) -> Dict[str, Any]:
        """Backup current configuration."""
        return {
            'max_threads': self.config.max_threads,
            'chunk_size': self.config.chunk_size,
            'parallel': self.config.parallel,
            'max_concurrent_tables': self.config.max_concurrent_tables
        }

    def _restore_config(self, backup: Dict[str, Any]):
        """Restore configuration from backup."""
        for key, value in backup.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)

    def _apply_scenario_config(self, scenario: Dict[str, Any]):
        """Apply scenario-specific configuration."""
        config_overrides = scenario.get('config', {})
        for key, value in config_overrides.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)

    def _run_scenario_test(self, test_tables: List[str], scenario: Dict[str, Any]) -> Dict[str, Any]:
        """Run a single test scenario."""
        from core.unified_table_processor import UnifiedTableProcessor

        # Limit tables for testing
        limited_tables = test_tables[:scenario.get('max_tables', 3)]

        # Start monitoring
        scenario_name = scenario.get('name', 'test_scenario')
        self.optimizer.start_operation_monitoring(scenario_name)

        try:
            # Create processor with current configuration
            strategy = 'parallel' if self.config.parallel else 'smart'
            processor = UnifiedTableProcessor(self.config, strategy=strategy)

            # Process tables
            start_time = time.time()
            result = processor.process_tables(limited_tables, skip_empty=True)
            duration = time.time() - start_time

            # Collect metrics
            summary = result.get('summary', {})
            performance_data = self.optimizer.end_operation_monitoring(
                scenario_name,
                tables_processed=summary.get('successful_backups', 0),
                rows_processed=summary.get('total_rows_backed_up', 0)
            )

            return {
                'status': 'success',
                'duration': duration,
                'tables_processed': summary.get('successful_backups', 0),
                'rows_processed': summary.get('total_rows_backed_up', 0),
                'failed_tables': summary.get('failed_backups', 0),
                'performance_data': performance_data,
                'config_used': self._backup_config()
            }

        except Exception as e:
            logger.error(f"Test scenario failed: {str(e)}")
            return {
                'status': 'error',
                'error': str(e),
                'config_used': self._backup_config()
            }

    def _generate_test_summary(self, scenarios: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate summary of test results."""
        successful_scenarios = [s for s in scenarios if s.get('status') == 'success']

        if not successful_scenarios:
            return {'status': 'no_successful_tests'}

        # Find best performing scenario
        best_scenario = min(successful_scenarios, key=lambda x: x.get('duration', float('inf')))

        # Calculate averages
        avg_duration = statistics.mean([s.get('duration', 0) for s in successful_scenarios])
        total_rows = sum([s.get('rows_processed', 0) for s in successful_scenarios])

        return {
            'status': 'success',
            'total_scenarios': len(scenarios),
            'successful_scenarios': len(successful_scenarios),
            'best_scenario': best_scenario.get('scenario', {}).get('name', 'Unknown'),
            'best_duration': best_scenario.get('duration', 0),
            'average_duration': avg_duration,
            'total_rows_processed': total_rows,
            'performance_improvement': self._calculate_improvement(scenarios)
        }

    def _calculate_improvement(self, scenarios: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate performance improvement between scenarios."""
        successful = [s for s in scenarios if s.get('status') == 'success']

        if len(successful) < 2:
            return {'status': 'insufficient_data'}

        baseline = successful[0]
        best = min(successful, key=lambda x: x.get('duration', float('inf')))

        if baseline.get('duration', 0) > 0:
            improvement_percent = ((baseline['duration'] - best['duration']) / baseline['duration']) * 100
            return {
                'status': 'calculated',
                'baseline_duration': baseline['duration'],
                'best_duration': best['duration'],
                'improvement_percent': improvement_percent,
                'baseline_config': baseline.get('config_used', {}),
                'best_config': best.get('config_used', {})
            }

        return {'status': 'calculation_error'}
