#!/usr/bin/env python3
"""
Enhanced Logging System for TNGD Backup System

This module provides a comprehensive logging solution with:
- Correlation ID tracking for operation traceability
- Structured logging with consistent formats
- Security-aware sensitive data filtering
- Performance-optimized logging with adaptive verbosity
- Hierarchical log organization by operation type
- Configurable log rotation and retention

Author: TNGD Backup System
Date: 2025-06-23
"""

import sys
import json
import logging
import logging.handlers
import datetime
import secrets
import threading
import re
from pathlib import Path
from typing import Dict, Any, Optional
from contextlib import contextmanager
from dataclasses import dataclass, field
from enum import Enum

# Security patterns for sensitive data filtering
SENSITIVE_PATTERNS = [
    # Credentials
    (re.compile(r'password["\s]*[:=]["\s]*[^"\s,}]+', re.IGNORECASE), 'password=***'),
    (re.compile(r'token["\s]*[:=]["\s]*[^"\s,}]+', re.IGNORECASE), 'token=***'),
    (re.compile(r'key["\s]*[:=]["\s]*[^"\s,}]+', re.IGNORECASE), 'key=***'),
    (re.compile(r'secret["\s]*[:=]["\s]*[^"\s,}]+', re.IGNORECASE), 'secret=***'),
    # File paths (show only relative paths)
    (re.compile(r'[A-Za-z]:\\[^"\s,}]+'), lambda m: f"...{m.group()[-30:]}"),
    (re.compile(r'/[^"\s,}]{20,}'), lambda m: f"...{m.group()[-30:]}"),
]


class LogLevel(Enum):
    """Enhanced log levels with clear usage guidelines."""
    CRITICAL = 50  # System failures, data corruption, security breaches
    ERROR = 40     # Operation failures that require attention
    WARNING = 30   # Issues that don't stop operations but need monitoring
    INFO = 20      # Normal operational information
    DEBUG = 10     # Detailed diagnostic information


class OperationType(Enum):
    """Types of operations for correlation tracking."""
    DAILY_BACKUP = "daily_backup"
    MONTHLY_BACKUP = "monthly_backup"
    HISTORICAL_BACKUP = "historical_backup"
    SINGLE_TABLE = "single_table"
    SYSTEM_MAINTENANCE = "system_maintenance"
    VALIDATION = "validation"


@dataclass
class LogContext:
    """Context information for logging operations."""
    correlation_id: str
    operation_type: OperationType
    component: str
    table_name: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    start_time: datetime.datetime = field(default_factory=datetime.datetime.now)


class SecurityFilter:
    """Filter sensitive information from log messages."""

    def __init__(self, enabled: bool = True, mask_file_paths: bool = True):
        self.enabled = enabled
        self.mask_file_paths = mask_file_paths

    def filter_message(self, message: str) -> str:
        """Filter sensitive data from log message."""
        if not self.enabled:
            return message

        filtered = message
        for pattern, replacement in SENSITIVE_PATTERNS:
            if callable(replacement):
                filtered = pattern.sub(replacement, filtered)
            else:
                filtered = pattern.sub(replacement, filtered)

        return filtered


class LogFormatter:
    """Standardized log message formatting."""

    def __init__(self, structured: bool = False, include_correlation: bool = True):
        self.structured = structured
        self.include_correlation = include_correlation

    def format_message(self, record: logging.LogRecord, context: Optional[LogContext] = None) -> str:
        """Format log message according to configuration."""
        if self.structured:
            return self._format_structured(record, context)
        else:
            return self._format_human_readable(record, context)

    def _format_structured(self, record: logging.LogRecord, context: Optional[LogContext]) -> str:
        """Format as structured JSON-like message."""
        log_entry = {
            "timestamp": datetime.datetime.fromtimestamp(record.created).isoformat(),
            "level": record.levelname,
            "component": getattr(record, 'component', record.name),
            "message": record.getMessage()
        }

        if context and self.include_correlation:
            log_entry.update({
                "correlation_id": context.correlation_id,
                "operation": context.operation_type.value,
                "phase": getattr(record, 'phase', 'PROCESS')
            })

            if context.table_name:
                log_entry["table_name"] = context.table_name

            if context.metadata:
                log_entry["metadata"] = context.metadata

        return json.dumps(log_entry, separators=(',', ':'))

    def _format_human_readable(self, record: logging.LogRecord, context: Optional[LogContext]) -> str:
        """Format as human-readable message."""
        timestamp = datetime.datetime.fromtimestamp(record.created).strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
        level = record.levelname
        component = getattr(record, 'component', record.name)
        message = record.getMessage()

        if context and self.include_correlation:
            correlation_part = f"[{context.correlation_id}] "
            operation_part = f"{component}.{context.operation_type.value}"
            phase = getattr(record, 'phase', 'PROCESS')

            if context.table_name:
                table_part = f" for {context.table_name}"
            else:
                table_part = ""

            return f"{timestamp} [{level}] {correlation_part}{operation_part}.{phase}: {message}{table_part}"
        else:
            return f"{timestamp} [{level}] {component}: {message}"


class EnhancedLogHandler(logging.Handler):
    """Custom log handler with security filtering and context awareness."""

    def __init__(self, base_handler: logging.Handler, security_filter: SecurityFilter,
                 formatter: LogFormatter, context: Optional[LogContext] = None):
        super().__init__()
        self.base_handler = base_handler
        self.security_filter = security_filter
        self.formatter = formatter
        self.context = context

    def emit(self, record: logging.LogRecord):
        """Emit a log record with security filtering and formatting."""
        try:
            # Filter sensitive data
            original_msg = record.getMessage()
            filtered_msg = self.security_filter.filter_message(original_msg)

            # Create a new record with filtered message
            filtered_record = logging.LogRecord(
                record.name, record.levelno, record.pathname, record.lineno,
                filtered_msg, record.args, record.exc_info, record.funcName
            )

            # Copy additional attributes
            for attr in ['component', 'phase']:
                if hasattr(record, attr):
                    setattr(filtered_record, attr, getattr(record, attr))

            # Format the message
            formatted_msg = self.formatter.format_message(filtered_record, self.context)

            # Create final record for base handler
            final_record = logging.LogRecord(
                record.name, record.levelno, record.pathname, record.lineno,
                formatted_msg, (), record.exc_info, record.funcName
            )

            self.base_handler.emit(final_record)

        except Exception:
            self.handleError(record)


class LogDirectoryManager:
    """Manages hierarchical log directory structure."""

    def __init__(self, base_dir: str = "logs"):
        self.base_dir = Path(base_dir)
        self._ensure_directory_structure()

    def _ensure_directory_structure(self):
        """Create the hierarchical log directory structure."""
        directories = [
            self.base_dir / "daily",
            self.base_dir / "monthly",
            self.base_dir / "historical",
            self.base_dir / "performance",
            self.base_dir / "security",
            self.base_dir / "system",
        ]

        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            (directory / "archive").mkdir(exist_ok=True)

    def get_log_path(self, operation_type: OperationType, log_type: str = "operations",
                     date: Optional[datetime.datetime] = None) -> Path:
        """Get the appropriate log file path for an operation."""
        if date is None:
            date = datetime.datetime.now()

        if operation_type == OperationType.DAILY_BACKUP:
            date_dir = self.base_dir / "daily" / date.strftime("%Y-%m-%d")
            date_dir.mkdir(exist_ok=True)
            return date_dir / f"{log_type}.log"

        elif operation_type == OperationType.MONTHLY_BACKUP:
            date_dir = self.base_dir / "monthly" / date.strftime("%Y-%m")
            date_dir.mkdir(exist_ok=True)
            return date_dir / f"{log_type}.log"

        elif operation_type == OperationType.HISTORICAL_BACKUP:
            return self.base_dir / "historical" / f"{log_type}.log"

        elif operation_type == OperationType.SINGLE_TABLE:
            date_dir = self.base_dir / "daily" / date.strftime("%Y-%m-%d")
            date_dir.mkdir(exist_ok=True)
            return date_dir / f"single_table_{log_type}.log"

        elif operation_type == OperationType.SYSTEM_MAINTENANCE:
            return self.base_dir / "system" / f"{log_type}.log"

        elif operation_type == OperationType.VALIDATION:
            date_dir = self.base_dir / "daily" / date.strftime("%Y-%m-%d")
            date_dir.mkdir(exist_ok=True)
            return date_dir / f"validation_{log_type}.log"

        else:
            # Default fallback
            return self.base_dir / "system" / f"{log_type}.log"


class CorrelationIDGenerator:
    """Generate unique correlation IDs for operation tracking."""

    @staticmethod
    def generate(operation_type: OperationType, suffix_length: int = 6) -> str:
        """Generate a correlation ID for an operation."""
        now = datetime.datetime.now()
        date_part = now.strftime("%Y%m%d_%H%M%S")
        suffix = secrets.token_hex(suffix_length // 2)
        return f"{operation_type.value}_{date_part}_{suffix}"


class EnhancedLogger:
    """
    Main enhanced logger class providing unified logging interface.

    Features:
    - Correlation ID tracking
    - Structured and human-readable logging
    - Security filtering
    - Hierarchical log organization
    - Performance optimization
    """

    def __init__(self, name: str, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the enhanced logger.

        Args:
            name: Logger name (typically module name)
            config: Optional configuration dictionary
        """
        self.name = name
        self.config = config or {}
        self._context_stack = threading.local()

        # Initialize components
        self.security_filter = SecurityFilter(
            enabled=self.config.get('security', {}).get('filter_sensitive_data', True),
            mask_file_paths=self.config.get('security', {}).get('mask_file_paths', True)
        )

        self.directory_manager = LogDirectoryManager(
            self.config.get('log_dir', 'logs')
        )

        # Initialize loggers for different purposes
        self._loggers = {}
        self._setup_loggers()

    def _setup_loggers(self):
        """Set up different loggers for various log types."""
        log_types = ['operations', 'errors', 'performance', 'security']

        for log_type in log_types:
            logger = logging.getLogger(f"{self.name}.{log_type}")
            logger.setLevel(getattr(logging, self.config.get('level', 'INFO')))

            # Remove existing handlers
            for handler in logger.handlers[:]:
                logger.removeHandler(handler)

            self._loggers[log_type] = logger

    def _get_current_context(self) -> Optional[LogContext]:
        """Get the current logging context from thread-local storage."""
        return getattr(self._context_stack, 'context', None)

    def _setup_handler_for_context(self, context: LogContext, log_type: str = 'operations'):
        """Set up a log handler for the given context."""
        logger = self._loggers.get(log_type, self._loggers['operations'])

        # Get appropriate log file path
        log_path = self.directory_manager.get_log_path(context.operation_type, log_type)

        # Create file handler with rotation
        file_handler = logging.handlers.RotatingFileHandler(
            log_path,
            maxBytes=self.config.get('rotation', {}).get('max_file_size_mb', 50) * 1024 * 1024,
            backupCount=self.config.get('rotation', {}).get('max_files', 10),
            encoding='utf-8'
        )

        # Create console handler if enabled
        console_handler = None
        if self.config.get('outputs', {}).get('console', True):
            console_handler = logging.StreamHandler(sys.stdout)

        # Create formatters
        structured = self.config.get('structured_format', False)
        include_correlation = self.config.get('correlation_tracking', True)

        file_formatter = LogFormatter(structured=structured, include_correlation=include_correlation)
        console_formatter = LogFormatter(structured=False, include_correlation=include_correlation)

        # Create enhanced handlers
        enhanced_file_handler = EnhancedLogHandler(
            file_handler, self.security_filter, file_formatter, context
        )

        logger.addHandler(enhanced_file_handler)

        if console_handler:
            enhanced_console_handler = EnhancedLogHandler(
                console_handler, self.security_filter, console_formatter, context
            )
            logger.addHandler(enhanced_console_handler)

    @contextmanager
    def operation_context(self, operation_type: OperationType, component: str,
                         table_name: Optional[str] = None, metadata: Optional[Dict[str, Any]] = None):
        """
        Context manager for logging operations with correlation tracking.

        Args:
            operation_type: Type of operation being performed
            component: Component/module performing the operation
            table_name: Optional table name for table-specific operations
            metadata: Optional metadata dictionary
        """
        # Generate correlation ID
        correlation_id = CorrelationIDGenerator.generate(operation_type)

        # Create context
        context = LogContext(
            correlation_id=correlation_id,
            operation_type=operation_type,
            component=component,
            table_name=table_name,
            metadata=metadata or {}
        )

        # Store in thread-local storage
        old_context = getattr(self._context_stack, 'context', None)
        self._context_stack.context = context

        # Set up handlers for this context
        self._setup_handler_for_context(context)

        try:
            yield context
        finally:
            # Restore previous context
            self._context_stack.context = old_context

            # Clean up handlers to prevent memory leaks
            for logger in self._loggers.values():
                for handler in logger.handlers[:]:
                    if isinstance(handler, EnhancedLogHandler):
                        logger.removeHandler(handler)

    def _log(self, level: int, message: str, component: Optional[str] = None,
             phase: Optional[str] = None, log_type: str = 'operations', **kwargs):
        """Internal logging method."""
        context = self._get_current_context()
        logger = self._loggers.get(log_type, self._loggers['operations'])

        # Create log record
        record = logger.makeRecord(
            logger.name, level, "", 0, message, (), None
        )

        # Add context attributes
        if component:
            record.component = component
        elif context:
            record.component = context.component
        else:
            record.component = self.name

        if phase:
            record.phase = phase

        # Add any additional attributes
        for key, value in kwargs.items():
            setattr(record, key, value)

        logger.handle(record)

    # Public logging methods
    def critical(self, message: str, component: Optional[str] = None, phase: Optional[str] = None, **kwargs):
        """Log a critical message."""
        self._log(logging.CRITICAL, message, component, phase, 'errors', **kwargs)

    def error(self, message: str, component: Optional[str] = None, phase: Optional[str] = None, **kwargs):
        """Log an error message."""
        self._log(logging.ERROR, message, component, phase, 'errors', **kwargs)

    def warning(self, message: str, component: Optional[str] = None, phase: Optional[str] = None, **kwargs):
        """Log a warning message."""
        self._log(logging.WARNING, message, component, phase, **kwargs)

    def info(self, message: str, component: Optional[str] = None, phase: Optional[str] = None, **kwargs):
        """Log an info message."""
        self._log(logging.INFO, message, component, phase, **kwargs)

    def debug(self, message: str, component: Optional[str] = None, phase: Optional[str] = None, **kwargs):
        """Log a debug message."""
        self._log(logging.DEBUG, message, component, phase, **kwargs)

    def performance(self, message: str, component: Optional[str] = None, **kwargs):
        """Log a performance-related message."""
        self._log(logging.INFO, message, component, 'PERFORMANCE', 'performance', **kwargs)

    def security(self, message: str, component: Optional[str] = None, **kwargs):
        """Log a security-related message."""
        self._log(logging.WARNING, message, component, 'SECURITY', 'security', **kwargs)

    def operation_start(self, operation: str, component: Optional[str] = None, **kwargs):
        """Log the start of an operation."""
        self.info(f"Starting {operation}", component, 'START', **kwargs)

    def operation_complete(self, operation: str, duration: Optional[float] = None,
                          component: Optional[str] = None, **kwargs):
        """Log the completion of an operation."""
        if duration is not None:
            message = f"Completed {operation} in {duration:.2f}s"
        else:
            message = f"Completed {operation}"
        self.info(message, component, 'COMPLETE', **kwargs)

    def operation_failed(self, operation: str, error: str, component: Optional[str] = None, **kwargs):
        """Log the failure of an operation."""
        self.error(f"Failed {operation}: {error}", component, 'FAILED', **kwargs)


# Global logger instance
_global_logger = None
_logger_lock = threading.Lock()


def get_logger(name: str, config: Optional[Dict[str, Any]] = None) -> EnhancedLogger:
    """
    Get or create an enhanced logger instance.

    Args:
        name: Logger name
        config: Optional configuration

    Returns:
        EnhancedLogger instance
    """
    global _global_logger

    with _logger_lock:
        if _global_logger is None:
            _global_logger = EnhancedLogger(name, config)
        return _global_logger


def configure_logging(config: Dict[str, Any]):
    """
    Configure the global logging system.

    Args:
        config: Logging configuration dictionary
    """
    global _global_logger

    with _logger_lock:
        _global_logger = EnhancedLogger("tngd_backup", config)


# Convenience functions for backward compatibility
def log_operation_start(operation: str, component: str = "general"):
    """Log operation start (backward compatibility)."""
    logger = get_logger("tngd_backup")
    logger.operation_start(operation, component)


def log_operation_complete(operation: str, duration: float, component: str = "general"):
    """Log operation completion (backward compatibility)."""
    logger = get_logger("tngd_backup")
    logger.operation_complete(operation, duration, component)


def log_operation_failed(operation: str, error: str, component: str = "general"):
    """Log operation failure (backward compatibility)."""
    logger = get_logger("tngd_backup")
    logger.operation_failed(operation, error, component)