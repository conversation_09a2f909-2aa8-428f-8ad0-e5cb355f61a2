# TNGD Backup System - Enhanced Logging Migration Guide

## Overview

This guide provides step-by-step instructions for migrating from the current logging system to the new enhanced logging architecture with correlation tracking, structured logging, and improved security.

## Migration Benefits

### Before (Current System)
- Basic file and console logging
- Inconsistent message formats
- No correlation tracking across operations
- Limited security features
- Manual log organization
- Basic error handling

### After (Enhanced System)
- **Correlation ID tracking** for operation traceability
- **Structured logging** with consistent formats
- **Security-aware logging** with sensitive data filtering
- **Hierarchical log organization** by operation type and date
- **Performance-optimized logging** with adaptive verbosity
- **Automated log rotation** and retention policies
- **Integrity verification** for critical log files

## Migration Steps

### Phase 1: Setup and Preparation

#### 1.1 Install Dependencies (Optional)
For enhanced performance monitoring:
```bash
pip install psutil
```

#### 1.2 Backup Current Logs
```bash
python -m utils.log_migration --backup
```

#### 1.3 Setup New Log Structure
```bash
python -m utils.log_migration --setup-structure
```

### Phase 2: Configuration Update

#### 2.1 Update config.json
Add the new logging configuration section to your `config.json`:

```json
{
  "logging": {
    "level": "INFO",
    "console_level": "INFO",
    "file_level": "DEBUG",
    "structured_format": false,
    "correlation_tracking": true,
    "security": {
      "filter_sensitive_data": true,
      "mask_file_paths": true,
      "enable_integrity_check": false
    },
    "performance": {
      "adaptive_logging": true,
      "async_logging": false,
      "buffer_size": 1000
    },
    "rotation": {
      "max_file_size_mb": 50,
      "max_files": 10,
      "compress_archived": true,
      "retention_days": 30
    },
    "outputs": {
      "console": true,
      "file": true,
      "structured_file": false,
      "syslog": false
    }
  }
}
```

#### 2.2 Configuration Options Explained

**Basic Settings:**
- `level`: Global log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
- `console_level`: Log level for console output
- `file_level`: Log level for file output
- `structured_format`: Enable JSON-structured logging
- `correlation_tracking`: Enable correlation ID tracking

**Security Settings:**
- `filter_sensitive_data`: Filter credentials and sensitive information
- `mask_file_paths`: Show only relative paths in logs
- `enable_integrity_check`: Enable cryptographic integrity verification

**Performance Settings:**
- `adaptive_logging`: Adjust verbosity based on system load
- `async_logging`: Use asynchronous logging (experimental)
- `buffer_size`: Buffer size for async logging

**Rotation Settings:**
- `max_file_size_mb`: Maximum file size before rotation
- `max_files`: Number of backup files to keep
- `compress_archived`: Compress rotated log files
- `retention_days`: Days to retain log files

### Phase 3: Code Migration

#### 3.1 Update Import Statements

**Old way:**
```python
from utils.minimal_logging import logger
```

**New way (Backward Compatible):**
```python
from utils.logging_adapter import logger
```

**New way (Enhanced Features):**
```python
from utils.logging_adapter import get_adapter, initialize_enhanced_logging
from utils.enhanced_logging import OperationType

# Initialize enhanced logging
initialize_enhanced_logging()
logger = get_adapter("my_module")
```

#### 3.2 Update Logging Patterns

**Old Pattern:**
```python
logger.info("Starting backup process")
logger.error(f"Backup failed: {error}")
```

**Enhanced Pattern with Context:**
```python
with logger.operation_context("daily_backup", "backup_processor") as context:
    logger.operation_start("backup_process", "backup_processor")

    try:
        # Your backup logic here
        logger.info("Processing table: my.app.tngd.waf", "table_processor", "PROCESS")

        # Update context with metadata
        context.metadata.update({
            "tables_processed": 5,
            "rows_backed_up": 50000
        })

        logger.operation_complete("backup_process", duration, "backup_processor")

    except Exception as e:
        logger.operation_failed("backup_process", str(e), "backup_processor")
```

#### 3.3 Structured Logging Examples

**Basic Structured Logging:**
```python
logger.log_structured(
    level="INFO",
    component="table_processor",
    phase="PROCESS",
    message="Processing chunk 1/5",
    metadata={
        "table_name": "my.app.tngd.waf",
        "chunk_size": 500000,
        "rows_processed": 50000
    }
)
```

**Performance Logging:**
```python
logger.performance(
    "Table backup completed in 45.2s",
    "table_processor",
    duration_ms=45200,
    rows_processed=1000000
)
```

**Security Logging:**
```python
logger.security(
    "Authentication successful",
    "auth_manager",
    user_id="user123",
    source_ip="*************"
)
```

### Phase 4: Migration Execution

#### 4.1 Migrate Existing Logs
```bash
# Dry run to see what will be migrated
python -m utils.log_migration --dry-run

# Perform actual migration
python -m utils.log_migration --migrate
```

#### 4.2 Update Module by Module

Start with non-critical modules and gradually migrate:

1. **Test modules first:**
   - Update test scripts
   - Verify logging output
   - Check correlation IDs

2. **Core modules:**
   - `core/unified_table_processor.py`
   - `core/backup_config.py`
   - `core/storage_manager.py`

3. **Scripts and utilities:**
   - `scripts/daily_backup_scheduler.py`
   - `utils/notification.py`
   - `utils/disk_cleanup.py`

#### 4.3 Cleanup Old Structure
After successful migration and testing:
```bash
python -m utils.log_migration --cleanup-old
```

### Phase 5: Verification and Testing

#### 5.1 Test Enhanced Features

**Test Correlation Tracking:**
```python
# Run a backup operation and verify correlation IDs appear in logs
python scripts/daily_backup_scheduler_enhanced.py --dry-run
```

**Test Security Features:**
```python
from utils.log_security import secure_log_file, verify_log_integrity
from pathlib import Path

# Secure a log file
log_file = Path("logs/daily/2025-06-23/operations.log")
secure_log_file(log_file)

# Verify integrity
is_valid, message = verify_log_integrity(log_file)
print(f"Integrity check: {message}")
```

**Test Performance Monitoring:**
```python
from utils.log_performance import get_system_performance_summary

summary = get_system_performance_summary()
print(f"System load: {summary['system_load']}")
```

#### 5.2 Verify Log Structure

Check that logs are organized correctly:
```
logs/
├── daily/
│   └── 2025-06-23/
│       ├── backup_operations.log
│       ├── table_processing.log
│       └── errors.log
├── monthly/
├── historical/
├── performance/
├── security/
└── system/
```

#### 5.3 Test Backward Compatibility

Ensure existing code still works:
```python
# This should still work without changes
from utils.logging_adapter import logger

logger.info("This is a test message")
logger.error("This is an error message")
```

## Advanced Features

### Security and Compliance

#### Enable Log Integrity Verification
```python
from utils.log_security import LogSecurityManager, SecurityLevel

security_manager = LogSecurityManager(security_level=SecurityLevel.AUDIT)

# Secure all log files
security_manager.secure_log_directory(Path("logs"))

# Generate compliance report
report = security_manager.generate_compliance_report(Path("compliance_report.json"))
```

#### Audit Trail
```python
from utils.log_security import LogAuditor

auditor = LogAuditor(security_manager)
auditor.log_security_event(
    "BACKUP_COMPLETED",
    {"tables": 50, "duration": "45m", "status": "success"},
    "INFO"
)
```

### Performance Optimization

#### Adaptive Logging
```python
from utils.log_performance import create_performance_aware_logger

# Create a logger that adapts to system load
adaptive_logger = create_performance_aware_logger(logger, monitor_interval=30)

# The logger will automatically reduce verbosity under high system load
adaptive_logger.log_with_performance_tracking(
    "INFO", "Processing large dataset", "data_processor"
)
```

#### Performance Metrics
```python
from utils.log_performance import LogMetricsCollector

collector = LogMetricsCollector()
summary = collector.get_performance_summary(operation_type="backup", last_n_minutes=60)
print(f"Average backup duration: {summary['avg_duration_ms']}ms")
```

## Troubleshooting

### Common Issues

#### 1. Import Errors
**Problem:** `ModuleNotFoundError: No module named 'utils.enhanced_logging'`

**Solution:** Ensure you're running from the project root directory and the new files are in place.

#### 2. Permission Errors
**Problem:** `PermissionError: [Errno 13] Permission denied`

**Solution:** Check file permissions and ensure the user has write access to the logs directory.

#### 3. Configuration Errors
**Problem:** Logging not working as expected

**Solution:** Verify the logging configuration in `config.json` and check for syntax errors.

#### 4. Performance Issues
**Problem:** Logging is slow or consuming too much memory

**Solution:**
- Enable adaptive logging
- Reduce log levels in production
- Check disk space and I/O performance

### Rollback Procedure

If you need to rollback to the old system:

1. **Restore old logs:**
   ```bash
   cp -r logs_backup/* logs/
   ```

2. **Revert import statements:**
   ```python
   # Change back to
   from utils.minimal_logging import logger
   ```

3. **Remove new configuration:**
   Remove the `logging` section from `config.json`

## Best Practices

### 1. Correlation ID Usage
- Always use operation contexts for multi-step operations
- Pass correlation IDs between related functions
- Include correlation IDs in error messages

### 2. Log Levels
- **DEBUG:** Detailed diagnostic information
- **INFO:** General operational information
- **WARNING:** Issues that don't stop operations
- **ERROR:** Operation failures requiring attention
- **CRITICAL:** System failures, data corruption

### 3. Security
- Never log passwords, tokens, or sensitive data
- Use relative paths instead of full file paths
- Enable integrity checking for audit logs
- Regularly review security logs

### 4. Performance
- Use adaptive logging in production
- Monitor log file sizes and rotation
- Consider async logging for high-throughput scenarios
- Regular cleanup of old log files

### 5. Monitoring
- Set up alerts for critical log messages
- Monitor log file growth and disk usage
- Review performance metrics regularly
- Use correlation IDs for troubleshooting

## Support and Maintenance

### Regular Tasks

#### Daily
- Monitor log file sizes
- Check for critical errors
- Verify backup operations

#### Weekly
- Review performance metrics
- Check log rotation status
- Validate security compliance

#### Monthly
- Generate compliance reports
- Review and update retention policies
- Performance optimization review

### Monitoring Integration

The enhanced logging system provides hooks for integration with monitoring systems:

```python
# Example: Send metrics to monitoring system
def send_to_monitoring(metrics_summary):
    # Your monitoring integration code here
    pass

# Get performance summary and send to monitoring
summary = collector.get_performance_summary()
send_to_monitoring(summary)
```

## Conclusion

The enhanced logging system provides significant improvements in traceability, security, and maintainability. Follow this migration guide carefully and test thoroughly in a development environment before deploying to production.

For questions or issues, refer to the troubleshooting section or review the architecture documentation in `LOGGING_ARCHITECTURE.md`.