# OSS Path Structure Update

## Overview

The TNGD backup system has been updated to use a consistent, configurable OSS path structure for all backup types. This change improves organization, maintainability, and ensures all backup files follow the same naming convention.

## Changes Made

### Previous Path Structure (Deprecated)
```
backup/daily/{date}/table_name_{timestamp}.tar.gz
```
**Example**: `backup/daily/2025-06-19/my_app_tngd_waf_20250619_042344.tar.gz`

### New Path Structure (Current)
```
Devo/{month_name_str}/week {week_number}/{date_str}/table_name_{timestamp}_{date_str}.tar.gz
```
**Example**: `Devo/June/week 3/2025-06-19/my_app_tngd_waf_20250619_044648_2025-06-19.tar.gz`

## Configuration

The path structure is controlled by the `oss_path_template` setting in `config.json`:

```json
{
  "storage": {
    "oss_path_template": "Devo/{month_name_str}/week {week_number}/{date_str}/{table_name}_{date_str}.tar.gz"
  }
}
```

### Template Variables

- `{month_name_str}` - Full month name (e.g., "June", "December")
- `{week_number}` - Week number within the month (1-5)
- `{date_str}` - Date in YYYY-MM-DD format
- `{table_name}` - Table name with timestamp (formatted for file naming)

### Week Number Calculation

Week numbers are calculated based on the day of the month:
- Week 1: Days 1-7
- Week 2: Days 8-14
- Week 3: Days 15-21
- Week 4: Days 22-28
- Week 5: Days 29-31 (if applicable)

## Benefits

### 1. **Improved Organization**
- Files are organized by month and week for easier navigation
- Consistent structure across all backup types
- Better support for long-term archival

### 2. **Enhanced Maintainability**
- Single configuration point for path structure
- Easy to modify path format without code changes
- Consistent with organizational standards

### 3. **Better Scalability**
- Hierarchical structure reduces files per directory
- Improved performance for large numbers of backups
- Better support for automated cleanup policies

### 4. **Backward Compatibility**
- Existing files in old structure remain accessible
- No data loss during transition
- Gradual migration possible

## Implementation Details

### Code Changes

1. **UnifiedTableProcessor** (`core/unified_table_processor.py`)
   - Updated `_process_single_table()` method
   - Now uses `ConfigManager.get_oss_path()` instead of hardcoded paths
   - Maintains timestamp uniqueness for file naming

2. **ConfigManager** (`core/config_manager.py`)
   - Enhanced `get_oss_path()` method
   - Supports configurable templates
   - Handles date formatting and week calculations

### Testing

The changes have been thoroughly tested:
- ✅ Path generation verification
- ✅ Single table backup test
- ✅ OSS upload verification
- ✅ File accessibility confirmation

## Migration Guide

### For Administrators

1. **No Action Required**: The system automatically uses the new path structure
2. **Old Files**: Remain accessible at their original locations
3. **Monitoring**: Check OSS console at new path: `Devo/{month}/week {n}/{date}/`

### For Developers

1. **Path References**: Update any hardcoded path references to use `ConfigManager.get_oss_path()`
2. **Testing**: Use the new path structure in test scripts
3. **Documentation**: Update any documentation referencing the old path structure

### For End Users

1. **OSS Console**: Navigate to `Devo/` directory instead of `backup/daily/`
2. **File Location**: New backups appear in month/week organized structure
3. **Access**: Old backups remain accessible at original locations

## Examples

### Current Date Examples (June 19, 2025)

**Single Table Backup**:
```
Devo/June/week 3/2025-06-19/my_app_tngd_waf_20250619_044648_2025-06-19.tar.gz
```

**Different Months**:
```
Devo/May/week 3/2025-05-15/my_app_tngd_test_20250515_090000_2025-05-15.tar.gz
Devo/July/week 1/2025-07-03/my_app_tngd_test_20250703_120000_2025-07-03.tar.gz
```

**Different Weeks**:
```
Devo/June/week 1/2025-06-01/my_app_tngd_test_20250601_120000_2025-06-01.tar.gz
Devo/June/week 4/2025-06-25/my_app_tngd_test_20250625_150000_2025-06-25.tar.gz
```

## Verification

To verify the new path structure is working:

1. **Run Test Script**:
   ```bash
   python test_oss_path_generation.py
   ```

2. **Check OSS Console**:
   - Navigate to `siem-security-logs` bucket
   - Look for `Devo/` directory
   - Verify files appear in month/week structure

3. **Run Single Table Backup**:
   ```bash
   python backup_single_table.py my.app.tngd.waf --days 1
   ```

## Troubleshooting

### Common Issues

1. **Files Not Found in OSS Console**
   - Check you're looking in `Devo/` not `backup/daily/`
   - Verify the correct month and week directories

2. **Path Generation Errors**
   - Ensure `config.json` has correct `oss_path_template`
   - Check date formatting in ConfigManager

3. **Backward Compatibility**
   - Old files remain at `backup/daily/{date}/`
   - New files appear at `Devo/{month}/week {n}/{date}/`

### Support

For issues or questions:
1. Check the logs in `logs/backup.log`
2. Run the test scripts to verify configuration
3. Review this documentation for path structure details

## Security Considerations

- ✅ **No Security Impact**: Path change doesn't affect security measures
- ✅ **Access Control**: Same OSS permissions apply to new structure
- ✅ **Encryption**: Server-side encryption continues to work
- ✅ **Integrity**: Checksum verification remains active

## Performance Impact

- ✅ **Minimal Impact**: Path generation adds negligible overhead
- ✅ **Improved Organization**: Better directory structure for large datasets
- ✅ **OSS Performance**: Hierarchical structure improves listing performance

---

**Last Updated**: June 19, 2025  
**Version**: 1.0  
**Status**: Active
