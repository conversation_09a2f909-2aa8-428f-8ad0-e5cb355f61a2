#!/usr/bin/env python3
"""
Enhanced Daily Backup Scheduler for TNGD

This is an updated version of the daily backup scheduler that demonstrates
the new enhanced logging patterns with correlation IDs, structured logging,
and improved traceability.

Key improvements:
- Uses enhanced logging with correlation tracking
- Proper operation context management
- Structured logging with metadata
- Security-aware logging (sensitive data filtering)
- Performance-optimized logging patterns

Usage:
    python scripts/daily_backup_scheduler_enhanced.py [options]
    python scripts/daily_backup_scheduler_enhanced.py --dry-run
    python scripts/daily_backup_scheduler_enhanced.py --single-table
"""

import sys
import datetime
from typing import Dict, Any, List, Optional
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Import project modules
from core.backup_config import BackupConfig
from core.config_manager import ConfigManager
from core.unified_table_processor import UnifiedTableProcessor
from utils.logging_adapter import get_adapter, initialize_enhanced_logging
from utils.enhanced_logging import OperationType
from utils.notification import send_backup_summary_notification
from utils.disk_cleanup import cleanup_temp_files


class EnhancedDailyBackupScheduler:
    """
    Enhanced daily backup scheduler with improved logging and traceability.

    This version demonstrates the new logging patterns:
    - Correlation ID tracking across operations
    - Structured logging with metadata
    - Security-aware logging
    - Performance-optimized logging
    """

    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """
        Initialize the enhanced daily backup scheduler.

        Args:
            config_manager: Configuration manager instance
        """
        self.config_manager = config_manager or ConfigManager()
        self.start_time = datetime.datetime.now()

        # Initialize enhanced logging
        logging_config = self.config_manager.get('logging', {})
        initialize_enhanced_logging(logging_config)
        self.logger = get_adapter("daily_backup_scheduler")

        # Initialize stats with more detailed tracking
        self.stats = {
            'total_tables': 0,
            'successful_tables': 0,
            'failed_tables': 0,
            'empty_tables_skipped': 0,
            'total_rows_backed_up': 0,
            'total_processing_time': 0,
            'failed_table_details': {},
            'performance_metrics': {},
            'correlation_id': None  # Will be set when operation starts
        }
        self.backup_date = None
        self.args = None

    def load_table_list(self) -> List[str]:
        """
        Load table names from configuration with enhanced logging.

        Returns:
            List of table names
        """
        try:
            self.logger.operation_start("load_table_list", "table_loader")

            tables = self.config_manager.get_tables_from_file()
            if not tables:
                self.logger.operation_failed("load_table_list", "No tables found in configuration", "table_loader")
                return []

            # Log success with metadata
            metadata = {"table_count": len(tables), "source": "tabletest/tables.json"}
            self.logger.info(f"Loaded {len(tables)} tables from configuration", "table_loader", "COMPLETE")

            return tables

        except Exception as e:
            self.logger.operation_failed("load_table_list", str(e), "table_loader")
            return []

    def create_backup_config(self, args) -> BackupConfig:
        """
        Create backup configuration with enhanced logging.

        Args:
            args: Command line arguments

        Returns:
            BackupConfig object
        """
        try:
            self.logger.operation_start("create_backup_config", "config_manager")

            # Create config from args
            config = BackupConfig.from_args(args, config_manager=self.config_manager)

            # Log configuration details (with security filtering)
            config_summary = {
                "days": config.days,
                "chunk_size": config.chunk_size,
                "timeout": config.timeout,
                "specific_date": config.specific_date.isoformat() if config.specific_date else None
            }

            self.logger.info("Backup configuration created", "config_manager", "COMPLETE")
            self.logger.debug(f"Config details: {config_summary}", "config_manager", "COMPLETE")

            return config

        except Exception as e:
            self.logger.operation_failed("create_backup_config", str(e), "config_manager")
            raise

    def run_daily_backup(self, args) -> Dict[str, Any]:
        """
        Execute the daily backup process with enhanced logging and correlation tracking.

        Args:
            args: Command line arguments

        Returns:
            Dictionary with backup results
        """
        # Use operation context for correlation tracking
        with self.logger.operation_context("daily_backup", "daily_backup_scheduler") as context:

            # Store correlation ID for stats
            self.stats['correlation_id'] = context.correlation_id

            self.logger.operation_start("daily_backup_process", "daily_backup_scheduler")

            try:
                # Load table list
                table_names = self.load_table_list()
                if not table_names:
                    error_msg = "No tables to backup"
                    self.logger.operation_failed("daily_backup_process", error_msg, "daily_backup_scheduler")
                    return {
                        'status': 'error',
                        'error': error_msg,
                        'stats': self.stats
                    }

                self.stats['total_tables'] = len(table_names)

                # Update context with metadata
                context.metadata.update({
                    "total_tables": len(table_names),
                    "backup_mode": self._get_backup_mode(args)
                })

                self.logger.info(f"Processing {len(table_names)} tables in {self._get_backup_mode(args)} mode",
                               "daily_backup_scheduler", "PROCESS")

                # Create backup configuration
                config = self.create_backup_config(args)

                # Execute based on mode
                if args.dry_run:
                    result = self._run_dry_run_validation(table_names, context)
                elif args.single_table:
                    result = self._run_single_table_test(table_names, config, context)
                else:
                    result = self._run_full_backup(table_names, config, context)

                # Log completion
                duration = (datetime.datetime.now() - self.start_time).total_seconds()
                self.logger.operation_complete("daily_backup_process", duration, "daily_backup_scheduler")

                return result

            except Exception as e:
                self.logger.operation_failed("daily_backup_process", str(e), "daily_backup_scheduler")
                return {
                    'status': 'error',
                    'error': str(e),
                    'stats': self.stats
                }

    def _get_backup_mode(self, args) -> str:
        """Get the backup mode string for logging."""
        if args.dry_run:
            return "dry_run"
        elif args.single_table:
            return "single_table"
        else:
            return "full_backup"

    def _run_dry_run_validation(self, table_names: List[str], context) -> Dict[str, Any]:
        """
        Run dry-run validation with enhanced logging.

        Args:
            table_names: List of table names to validate
            context: Logging context

        Returns:
            Validation results
        """
        self.logger.info("Starting dry-run validation (no actual backup)", "validator", "START")

        # Update context metadata
        context.metadata.update({
            "validation_mode": True,
            "tables_to_validate": len(table_names)
        })

        validation_stats = {
            'accessible': 0,
            'with_data': 0,
            'empty': 0,
            'inaccessible': 0
        }

        for i, table_name in enumerate(table_names, 1):
            try:
                # Update context with current table
                context.table_name = table_name
                context.metadata.update({
                    "current_table": table_name,
                    "progress": f"{i}/{len(table_names)}"
                })

                self.logger.info(f"Validating table {i}/{len(table_names)}: {table_name}", "validator", "PROCESS")

                # Simulate validation (in real implementation, this would check table accessibility)
                validation_stats['accessible'] += 1
                validation_stats['with_data'] += 1

            except Exception as e:
                self.logger.error(f"Validation failed for {table_name}: {str(e)}", "validator", "FAILED")
                validation_stats['inaccessible'] += 1

        # Log validation summary
        self.logger.info(f"Validation complete: {validation_stats}", "validator", "COMPLETE")

        return {
            'status': 'success',
            'mode': 'dry_run',
            'validation_stats': validation_stats,
            'stats': self.stats
        }

    def _run_single_table_test(self, table_names: List[str], config: BackupConfig, context) -> Dict[str, Any]:
        """
        Run single table test with enhanced logging.

        Args:
            table_names: List of table names
            config: Backup configuration
            context: Logging context

        Returns:
            Test results
        """
        test_table = table_names[0] if table_names else "my.app.tngd.waf"

        # Update context
        context.table_name = test_table
        context.metadata.update({
            "test_mode": True,
            "test_table": test_table
        })

        self.logger.info(f"Starting single table test with: {test_table}", "table_processor", "START")

        try:
            # Use unified table processor for single table
            processor = UnifiedTableProcessor(config, strategy='smart')
            result = processor.process_tables([test_table], skip_empty=False)

            self.logger.info(f"Single table test completed successfully", "table_processor", "COMPLETE")

            return {
                'status': 'success',
                'mode': 'single_table_test',
                'test_table': test_table,
                'result': result,
                'stats': self.stats
            }

        except Exception as e:
            self.logger.error(f"Single table test failed: {str(e)}", "table_processor", "FAILED")
            return {
                'status': 'error',
                'mode': 'single_table_test',
                'test_table': test_table,
                'error': str(e),
                'stats': self.stats
            }

    def _run_full_backup(self, table_names: List[str], config: BackupConfig, context) -> Dict[str, Any]:
        """
        Run full backup with enhanced logging and progress tracking.

        Args:
            table_names: List of table names
            config: Backup configuration
            context: Logging context

        Returns:
            Backup results
        """
        self.logger.info("Starting full daily backup process", "backup_processor", "START")

        # Update context metadata
        context.metadata.update({
            "full_backup_mode": True,
            "total_tables": len(table_names),
            "skip_empty": True
        })

        try:
            # Use unified table processor for sequential processing
            processor = UnifiedTableProcessor(config, strategy='smart')

            # Process tables with progress tracking
            start_time = datetime.datetime.now()
            result = processor.process_tables(table_names, skip_empty=True)
            duration = (datetime.datetime.now() - start_time).total_seconds()

            # Update stats from result
            summary = result.get('summary', {})
            self.stats.update({
                'successful_tables': summary.get('successful_backups', 0),
                'failed_tables': summary.get('failed_backups', 0),
                'empty_tables_skipped': summary.get('empty_tables', 0),
                'total_rows_backed_up': summary.get('total_rows_backed_up', 0),
                'total_processing_time': summary.get('total_processing_time', 0)
            })

            # Update context with final stats
            context.metadata.update({
                "successful_tables": self.stats['successful_tables'],
                "failed_tables": self.stats['failed_tables'],
                "total_rows": self.stats['total_rows_backed_up'],
                "duration_seconds": duration
            })

            # Log completion with performance metrics
            success_rate = (self.stats['successful_tables'] / len(table_names) * 100) if table_names else 0
            self.logger.performance(
                f"Full backup completed: {success_rate:.1f}% success rate, {self.stats['total_rows_backed_up']} rows, {duration:.1f}s",
                "backup_processor"
            )

            status = 'success' if summary.get('failed_backups', 0) == 0 else 'partial'
            return {
                'status': status,
                'mode': 'full_backup',
                'result': result,
                'stats': self.stats
            }

        except Exception as e:
            self.logger.error(f"Full backup failed: {str(e)}", "backup_processor", "FAILED")
            return {
                'status': 'error',
                'mode': 'full_backup',
                'error': str(e),
                'stats': self.stats
            }


# Example usage and demonstration
def main():
    """
    Main function demonstrating the enhanced logging patterns.

    This is a simplified version for demonstration purposes.
    """
    print("Enhanced Daily Backup Scheduler - Logging Demonstration")
    print("=" * 60)

    # Create scheduler instance
    scheduler = EnhancedDailyBackupScheduler()

    # Simulate command line arguments
    class MockArgs:
        dry_run = True
        single_table = False
        date = None
        days = 1
        chunk_size = None
        timeout = None
        max_retries = None
        max_threads = None
        max_concurrent_tables = None
        batch_size = None
        table_timeout = None
        tables = None
        mode = None
        month = None
        year = None
        chunking_strategy = None
        monthly_max_retries = None
        monthly_retry_delay = None

    args = MockArgs()

    # Run the backup process
    result = scheduler.run_daily_backup(args)

    print(f"\nBackup Result: {result}")
    print(f"Correlation ID: {scheduler.stats.get('correlation_id', 'N/A')}")

    return 0


if __name__ == "__main__":
    sys.exit(main())